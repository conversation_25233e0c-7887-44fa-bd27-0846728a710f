﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using SubscriptionManagement.Infrastructure.Persistence;

#nullable disable

namespace SubscriptionManagement.Infrastructure.Migrations
{
    [DbContext(typeof(SubscriptionDbContext))]
    partial class SubscriptionDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.11")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("SubscriptionManagement.Domain.Entities.FeatureFlag", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ABTestConfiguration")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DefaultValue")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid?>("MenuId")
                        .HasColumnType("uuid");

                    b.Property<string>("Metadata")
                        .HasColumnType("jsonb");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("RolloutPercentage")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TargetAudience")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("UserType")
                        .HasColumnType("integer");

                    b.Property<string>("Variants")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.HasKey("Id");

                    b.HasIndex("Key")
                        .IsUnique();

                    b.HasIndex("MenuId");

                    b.HasIndex("Name");

                    b.HasIndex("Status");

                    b.HasIndex("Type");

                    b.HasIndex("UserType");

                    b.ToTable("FeatureFlags", (string)null);
                });

            modelBuilder.Entity("SubscriptionManagement.Domain.Entities.FeatureFlagRule", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Condition")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<Guid>("FeatureFlagId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("Priority")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("IsActive");

                    b.HasIndex("Priority");

                    b.HasIndex("Type");

                    b.HasIndex("FeatureFlagId", "IsActive");

                    b.ToTable("FeatureFlagRules", (string)null);
                });

            modelBuilder.Entity("SubscriptionManagement.Domain.Entities.FeatureFlagUsage", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("AccessedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Context")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("FeatureFlagId")
                        .HasColumnType("uuid");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<string>("SessionId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UserAgent")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Variant")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("AccessedAt");

                    b.HasIndex("SessionId");

                    b.HasIndex("UserId");

                    b.HasIndex("FeatureFlagId", "UserId");

                    b.HasIndex("FeatureFlagId", "Variant");

                    b.ToTable("FeatureFlagUsage", (string)null);
                });

            modelBuilder.Entity("SubscriptionManagement.Domain.Entities.GlobalTaxConfiguration", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<int>("Priority")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("CreatedByUserId");

                    b.HasIndex("IsActive");

                    b.HasIndex("Priority");

                    b.HasIndex("IsActive", "Priority");

                    b.ToTable("GlobalTaxConfigurations", (string)null);
                });

            modelBuilder.Entity("SubscriptionManagement.Domain.Entities.NotificationHistory", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<string>("Body")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Channel")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("ClickedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("DeliveredAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeliveryMetadata")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<string>("ErrorCode")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("ExternalNotificationId")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime?>("LastRetryAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Metadata")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<DateTime?>("ReadAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("RetryCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<DateTime?>("ScheduledAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("SentAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Status")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasDefaultValue("Pending");

                    b.Property<string>("Subject")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<Guid?>("SubscriptionId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("TriggeredByUserId")
                        .HasColumnType("uuid");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("Channel")
                        .HasDatabaseName("IX_NotificationHistories_Channel");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_NotificationHistories_CreatedAt");

                    b.HasIndex("DeliveredAt")
                        .HasDatabaseName("IX_NotificationHistories_DeliveredAt");

                    b.HasIndex("ExternalNotificationId")
                        .HasDatabaseName("IX_NotificationHistories_ExternalNotificationId");

                    b.HasIndex("ScheduledAt")
                        .HasDatabaseName("IX_NotificationHistories_ScheduledAt");

                    b.HasIndex("SentAt")
                        .HasDatabaseName("IX_NotificationHistories_SentAt");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_NotificationHistories_Status");

                    b.HasIndex("SubscriptionId")
                        .HasDatabaseName("IX_NotificationHistories_SubscriptionId");

                    b.HasIndex("TriggeredByUserId")
                        .HasDatabaseName("IX_NotificationHistories_TriggeredByUserId");

                    b.HasIndex("Type")
                        .HasDatabaseName("IX_NotificationHistories_Type");

                    b.HasIndex("UserId")
                        .HasDatabaseName("IX_NotificationHistories_UserId");

                    b.HasIndex("Channel", "Status")
                        .HasDatabaseName("IX_NotificationHistories_Channel_Status");

                    b.HasIndex("Status", "CreatedAt")
                        .HasDatabaseName("IX_NotificationHistories_Status_CreatedAt");

                    b.HasIndex("Type", "Status")
                        .HasDatabaseName("IX_NotificationHistories_Type_Status");

                    b.HasIndex("Status", "RetryCount", "LastRetryAt")
                        .HasDatabaseName("IX_NotificationHistories_Status_RetryCount_LastRetryAt");

                    b.HasIndex("SubscriptionId", "Type", "CreatedAt")
                        .HasDatabaseName("IX_NotificationHistories_SubscriptionId_Type_CreatedAt");

                    b.HasIndex("UserId", "Type", "CreatedAt")
                        .HasDatabaseName("IX_NotificationHistories_UserId_Type_CreatedAt");

                    b.ToTable("NotificationHistories", (string)null);
                });

            modelBuilder.Entity("SubscriptionManagement.Domain.Entities.NotificationTemplate", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<string>("Body")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Channel")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<string>("Language")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)")
                        .HasDefaultValue("en");

                    b.Property<string>("Metadata")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("Subject")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Variables")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.HasKey("Id");

                    b.HasIndex("Channel")
                        .HasDatabaseName("IX_NotificationTemplates_Channel");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_NotificationTemplates_IsActive");

                    b.HasIndex("Language")
                        .HasDatabaseName("IX_NotificationTemplates_Language");

                    b.HasIndex("Name")
                        .IsUnique()
                        .HasDatabaseName("IX_NotificationTemplates_Name");

                    b.HasIndex("Type")
                        .HasDatabaseName("IX_NotificationTemplates_Type");

                    b.HasIndex("Channel", "IsActive")
                        .HasDatabaseName("IX_NotificationTemplates_Channel_IsActive");

                    b.HasIndex("Type", "IsActive")
                        .HasDatabaseName("IX_NotificationTemplates_Type_IsActive");

                    b.HasIndex("Type", "Channel", "Language", "IsActive")
                        .HasDatabaseName("IX_NotificationTemplates_Type_Channel_Language_IsActive");

                    b.ToTable("NotificationTemplates", (string)null);
                });

            modelBuilder.Entity("SubscriptionManagement.Domain.Entities.Payment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("FailedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("FailureReason")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime?>("NextRetryAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("PaymentGatewayResponse")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("PaymentGatewayTransactionId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("PaymentMethod")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("ProcessedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("RetryCount")
                        .HasColumnType("integer");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("SubscriptionId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("SubscriptionId1")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("PaymentGatewayTransactionId");

                    b.HasIndex("ProcessedAt");

                    b.HasIndex("Status");

                    b.HasIndex("SubscriptionId");

                    b.HasIndex("SubscriptionId1");

                    b.HasIndex("UserId");

                    b.ToTable("Payments", (string)null);
                });

            modelBuilder.Entity("SubscriptionManagement.Domain.Entities.Plan", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsPublic")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid>("PlanId")
                        .HasColumnType("uuid")
                        .HasComment("Logical plan identifier that groups all versions of the same plan");

                    b.Property<int?>("SetupFeeAmount")
                        .HasColumnType("integer");

                    b.Property<string>("SetupFeeCurrency")
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)");

                    b.Property<Guid?>("TaxCategoryId")
                        .HasColumnType("uuid");

                    b.Property<int?>("TrialPeriodDays")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UserType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Version")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1);

                    b.HasKey("Id");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_Plans_IsActive");

                    b.HasIndex("IsPublic");

                    b.HasIndex("Name");

                    b.HasIndex("PlanId")
                        .HasDatabaseName("IX_Plans_PlanId");

                    b.HasIndex("TaxCategoryId");

                    b.HasIndex("Version")
                        .HasDatabaseName("IX_Plans_Version");

                    b.HasIndex("PlanId", "Version")
                        .IsUnique()
                        .HasDatabaseName("UQ_Plans_PlanId_Version");

                    b.HasIndex("UserType", "Type")
                        .HasDatabaseName("IX_Plans_UserType_Type");

                    b.ToTable("Plans", (string)null);
                });

            modelBuilder.Entity("SubscriptionManagement.Domain.Entities.PlanFeature", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<Guid>("FeatureId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<Guid?>("PlanId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("Key");

                    b.HasIndex("PlanId", "FeatureId")
                        .IsUnique();

                    b.ToTable("PlanFeatures", (string)null);
                });

            modelBuilder.Entity("SubscriptionManagement.Domain.Entities.PlanTaxConfiguration", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("EffectiveFrom")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("EffectiveTo")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<Guid>("PlanId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("EffectiveFrom");

                    b.HasIndex("EffectiveTo");

                    b.HasIndex("IsActive");

                    b.HasIndex("PlanId");

                    b.HasIndex("PlanId", "IsActive");

                    b.HasIndex("PlanId", "EffectiveFrom", "EffectiveTo");

                    b.ToTable("PlanTaxConfigurations", (string)null);
                });

            modelBuilder.Entity("SubscriptionManagement.Domain.Entities.Subscription", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("AutoRenew")
                        .HasColumnType("boolean");

                    b.Property<string>("CancellationReason")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<long?>("CancelledAt")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("EndDate")
                        .HasColumnType("bigint");

                    b.Property<Guid?>("ExtendedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("ExtensionReason")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<long?>("GracePeriodEndDate")
                        .HasColumnType("bigint");

                    b.Property<long?>("LastExtendedAt")
                        .HasColumnType("bigint");

                    b.Property<long>("NextBillingDate")
                        .HasColumnType("bigint");

                    b.Property<string>("PaymentMethodId")
                        .HasColumnType("text");

                    b.Property<Guid>("PlanId")
                        .HasColumnType("uuid");

                    b.Property<int>("PlanVersion")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1);

                    b.Property<string>("ProrationMode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("StartDate")
                        .HasColumnType("bigint");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long?>("TrialEndDate")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ExtendedByUserId");

                    b.HasIndex("GracePeriodEndDate");

                    b.HasIndex("LastExtendedAt");

                    b.HasIndex("NextBillingDate");

                    b.HasIndex("PlanId");

                    b.HasIndex("Status");

                    b.HasIndex("UserId");

                    b.HasIndex("UserId", "Status");

                    b.ToTable("Subscriptions", (string)null);
                });

            modelBuilder.Entity("SubscriptionManagement.Domain.Entities.SubscriptionChange", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ChangeType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("ChangedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<Guid?>("SubscriptionId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("ChangedAt");

                    b.HasIndex("SubscriptionId");

                    b.ToTable("SubscriptionChanges", (string)null);
                });

            modelBuilder.Entity("SubscriptionManagement.Domain.Entities.SubscriptionPaymentProof", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime>("PaymentDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("PaymentMethod")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ProofImageUrl")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("RejectionReason")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<Guid>("SubscriptionId")
                        .HasColumnType("uuid");

                    b.Property<string>("TransactionReference")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.Property<string>("VerificationNotes")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime?>("VerifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("VerifiedByUserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_SubscriptionPaymentProofs_CreatedAt");

                    b.HasIndex("PaymentDate")
                        .HasDatabaseName("IX_SubscriptionPaymentProofs_PaymentDate");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_SubscriptionPaymentProofs_Status");

                    b.HasIndex("SubscriptionId")
                        .HasDatabaseName("IX_SubscriptionPaymentProofs_SubscriptionId");

                    b.HasIndex("UserId")
                        .HasDatabaseName("IX_SubscriptionPaymentProofs_UserId");

                    b.HasIndex("VerifiedAt")
                        .HasDatabaseName("IX_SubscriptionPaymentProofs_VerifiedAt");

                    b.HasIndex("Status", "CreatedAt")
                        .HasDatabaseName("IX_SubscriptionPaymentProofs_Status_CreatedAt");

                    b.HasIndex("SubscriptionId", "Status")
                        .HasDatabaseName("IX_SubscriptionPaymentProofs_SubscriptionId_Status");

                    b.HasIndex("UserId", "Status")
                        .HasDatabaseName("IX_SubscriptionPaymentProofs_UserId_Status");

                    b.ToTable("SubscriptionPaymentProofs", (string)null);
                });

            modelBuilder.Entity("SubscriptionManagement.Domain.Entities.TaxCategory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.HasIndex("CreatedAt");

                    b.HasIndex("IsActive");

                    b.HasIndex("Name");

                    b.ToTable("TaxCategories", (string)null);
                });

            modelBuilder.Entity("SubscriptionManagement.Domain.Entities.TaxExemption", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ApplicableRegions")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)")
                        .HasColumnName("ApplicableRegions");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DocumentPath")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("ExemptTaxTypes")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("ExemptTaxTypes");

                    b.Property<string>("ExemptionNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ExemptionType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("IssuingAuthority")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("ValidFrom")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("ValidTo")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("VerificationNotes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("VerifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("VerifiedByUserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("ExemptionNumber");

                    b.HasIndex("IsActive");

                    b.HasIndex("UserId");

                    b.HasIndex("ValidFrom");

                    b.HasIndex("ValidTo");

                    b.HasIndex("VerifiedAt");

                    b.HasIndex("ExemptionNumber", "UserId")
                        .IsUnique();

                    b.HasIndex("UserId", "IsActive");

                    b.ToTable("TaxExemptions", (string)null);
                });

            modelBuilder.Entity("SubscriptionManagement.Domain.Entities.TierConfigurationSetup", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime>("CreatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool?>("IsCustom")
                        .HasColumnType("boolean");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime?>("UpdatedOn")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CreatedBy")
                        .HasDatabaseName("IX_TierConfigurationSetup_CreatedBy");

                    b.HasIndex("CreatedOn")
                        .HasDatabaseName("IX_TierConfigurationSetup_CreatedOn");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_TierConfigurationSetup_IsActive");

                    b.HasIndex("Type")
                        .HasDatabaseName("IX_TierConfigurationSetup_Type");

                    b.ToTable("TierConfigurationSetup", (string)null);
                });

            modelBuilder.Entity("SubscriptionManagement.Domain.Entities.UsageRecord", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("FeatureKey")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Metadata")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime>("PeriodEnd")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("PeriodStart")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("SubscriptionId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("UsageCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1);

                    b.Property<DateTime>("UsageDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("FeatureKey");

                    b.HasIndex("SubscriptionId");

                    b.HasIndex("UsageDate");

                    b.HasIndex("UserId");

                    b.HasIndex("SubscriptionId", "FeatureKey", "PeriodStart", "PeriodEnd");

                    b.HasIndex("UserId", "FeatureKey", "PeriodStart", "PeriodEnd");

                    b.ToTable("UsageRecords", (string)null);
                });

            modelBuilder.Entity("SubscriptionManagement.Domain.Entities.FeatureFlagRule", b =>
                {
                    b.HasOne("SubscriptionManagement.Domain.Entities.FeatureFlag", null)
                        .WithMany("Rules")
                        .HasForeignKey("FeatureFlagId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("SubscriptionManagement.Domain.Entities.FeatureFlagUsage", b =>
                {
                    b.HasOne("SubscriptionManagement.Domain.Entities.FeatureFlag", null)
                        .WithMany("UsageHistory")
                        .HasForeignKey("FeatureFlagId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("SubscriptionManagement.Domain.Entities.GlobalTaxConfiguration", b =>
                {
                    b.OwnsOne("SubscriptionManagement.Domain.ValueObjects.TaxConfiguration", "TaxConfiguration", b1 =>
                        {
                            b1.Property<Guid>("GlobalTaxConfigurationId")
                                .HasColumnType("uuid");

                            b1.Property<string>("ApplicableRegions")
                                .IsRequired()
                                .HasMaxLength(1000)
                                .HasColumnType("character varying(1000)")
                                .HasColumnName("ApplicableRegions");

                            b1.Property<DateTime>("EffectiveDate")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("EffectiveDate");

                            b1.Property<DateTime?>("ExpirationDate")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("ExpirationDate");

                            b1.Property<bool>("IsIncluded")
                                .HasColumnType("boolean")
                                .HasColumnName("IsIncluded");

                            b1.Property<decimal>("Rate")
                                .HasColumnType("decimal(5,4)")
                                .HasColumnName("Rate");

                            b1.Property<string>("TaxType")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("TaxType");

                            b1.HasKey("GlobalTaxConfigurationId");

                            b1.ToTable("GlobalTaxConfigurations");

                            b1.WithOwner()
                                .HasForeignKey("GlobalTaxConfigurationId");
                        });

                    b.Navigation("TaxConfiguration")
                        .IsRequired();
                });

            modelBuilder.Entity("SubscriptionManagement.Domain.Entities.Payment", b =>
                {
                    b.HasOne("SubscriptionManagement.Domain.Entities.Subscription", null)
                        .WithMany("Payments")
                        .HasForeignKey("SubscriptionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("SubscriptionManagement.Domain.Entities.Subscription", "Subscription")
                        .WithMany()
                        .HasForeignKey("SubscriptionId1");

                    b.OwnsOne("SubscriptionManagement.Domain.ValueObjects.Money", "Amount", b1 =>
                        {
                            b1.Property<Guid>("PaymentId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("Amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("Currency");

                            b1.HasKey("PaymentId");

                            b1.ToTable("Payments");

                            b1.WithOwner()
                                .HasForeignKey("PaymentId");
                        });

                    b.Navigation("Amount")
                        .IsRequired();

                    b.Navigation("Subscription");
                });

            modelBuilder.Entity("SubscriptionManagement.Domain.Entities.Plan", b =>
                {
                    b.HasOne("SubscriptionManagement.Domain.Entities.TaxCategory", "TaxCategory")
                        .WithMany()
                        .HasForeignKey("TaxCategoryId");

                    b.OwnsOne("SubscriptionManagement.Domain.ValueObjects.BillingCycle", "BillingCycle", b1 =>
                        {
                            b1.Property<Guid>("PlanId")
                                .HasColumnType("uuid");

                            b1.Property<int?>("CustomDays")
                                .HasColumnType("integer")
                                .HasColumnName("CustomBillingDays");

                            b1.Property<string>("Interval")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("BillingInterval");

                            b1.Property<int>("IntervalCount")
                                .HasColumnType("integer")
                                .HasColumnName("BillingIntervalCount");

                            b1.HasKey("PlanId");

                            b1.ToTable("Plans");

                            b1.WithOwner()
                                .HasForeignKey("PlanId");
                        });

                    b.OwnsOne("SubscriptionManagement.Domain.ValueObjects.Money", "Price", b1 =>
                        {
                            b1.Property<Guid>("PlanId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("Price");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("Currency");

                            b1.HasKey("PlanId");

                            b1.ToTable("Plans");

                            b1.WithOwner()
                                .HasForeignKey("PlanId");
                        });

                    b.OwnsOne("SubscriptionManagement.Domain.ValueObjects.PlanLimits", "Limits", b1 =>
                        {
                            b1.Property<Guid>("PlanId")
                                .HasColumnType("uuid");

                            b1.Property<int?>("CarrierLimit")
                                .HasColumnType("integer")
                                .HasColumnName("CarrierLimit");

                            b1.Property<bool>("IsUnlimited")
                                .HasColumnType("boolean")
                                .HasColumnName("IsUnlimited");

                            b1.Property<int?>("RfqLimit")
                                .HasColumnType("integer")
                                .HasColumnName("RfqLimit");

                            b1.Property<int?>("VehicleLimit")
                                .HasColumnType("integer")
                                .HasColumnName("VehicleLimit");

                            b1.HasKey("PlanId");

                            b1.ToTable("Plans");

                            b1.WithOwner()
                                .HasForeignKey("PlanId");
                        });

                    b.Navigation("BillingCycle")
                        .IsRequired();

                    b.Navigation("Limits")
                        .IsRequired();

                    b.Navigation("Price")
                        .IsRequired();

                    b.Navigation("TaxCategory");
                });

            modelBuilder.Entity("SubscriptionManagement.Domain.Entities.PlanFeature", b =>
                {
                    b.HasOne("SubscriptionManagement.Domain.Entities.Plan", null)
                        .WithMany("Features")
                        .HasForeignKey("PlanId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("SubscriptionManagement.Domain.Entities.PlanTaxConfiguration", b =>
                {
                    b.HasOne("SubscriptionManagement.Domain.Entities.Plan", "Plan")
                        .WithMany("PlanTaxConfigurations")
                        .HasForeignKey("PlanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("SubscriptionManagement.Domain.ValueObjects.TaxConfiguration", "TaxConfiguration", b1 =>
                        {
                            b1.Property<Guid>("PlanTaxConfigurationId")
                                .HasColumnType("uuid");

                            b1.Property<string>("ApplicableRegions")
                                .IsRequired()
                                .HasMaxLength(1000)
                                .HasColumnType("character varying(1000)")
                                .HasColumnName("ApplicableRegions");

                            b1.Property<DateTime>("EffectiveDate")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("TaxEffectiveDate");

                            b1.Property<DateTime?>("ExpirationDate")
                                .HasColumnType("timestamp with time zone")
                                .HasColumnName("TaxExpirationDate");

                            b1.Property<bool>("IsIncluded")
                                .HasColumnType("boolean")
                                .HasColumnName("IsIncluded");

                            b1.Property<decimal>("Rate")
                                .HasColumnType("decimal(5,4)")
                                .HasColumnName("Rate");

                            b1.Property<string>("TaxType")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("TaxType");

                            b1.HasKey("PlanTaxConfigurationId");

                            b1.ToTable("PlanTaxConfigurations");

                            b1.WithOwner()
                                .HasForeignKey("PlanTaxConfigurationId");
                        });

                    b.Navigation("Plan");

                    b.Navigation("TaxConfiguration")
                        .IsRequired();
                });

            modelBuilder.Entity("SubscriptionManagement.Domain.Entities.Subscription", b =>
                {
                    b.HasOne("SubscriptionManagement.Domain.Entities.Plan", "Plan")
                        .WithMany()
                        .HasForeignKey("PlanId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.OwnsOne("SubscriptionManagement.Domain.ValueObjects.BillingCycle", "BillingCycle", b1 =>
                        {
                            b1.Property<Guid>("SubscriptionId")
                                .HasColumnType("uuid");

                            b1.Property<int?>("CustomDays")
                                .HasColumnType("integer")
                                .HasColumnName("CustomBillingDays");

                            b1.Property<string>("Interval")
                                .IsRequired()
                                .HasColumnType("text")
                                .HasColumnName("BillingInterval");

                            b1.Property<int>("IntervalCount")
                                .HasColumnType("integer")
                                .HasColumnName("BillingIntervalCount");

                            b1.HasKey("SubscriptionId");

                            b1.ToTable("Subscriptions");

                            b1.WithOwner()
                                .HasForeignKey("SubscriptionId");
                        });

                    b.OwnsOne("SubscriptionManagement.Domain.ValueObjects.Money", "CurrentPrice", b1 =>
                        {
                            b1.Property<Guid>("SubscriptionId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("CurrentPrice");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("CurrentCurrency");

                            b1.HasKey("SubscriptionId");

                            b1.ToTable("Subscriptions");

                            b1.WithOwner()
                                .HasForeignKey("SubscriptionId");
                        });

                    b.Navigation("BillingCycle")
                        .IsRequired();

                    b.Navigation("CurrentPrice")
                        .IsRequired();

                    b.Navigation("Plan");
                });

            modelBuilder.Entity("SubscriptionManagement.Domain.Entities.SubscriptionChange", b =>
                {
                    b.HasOne("SubscriptionManagement.Domain.Entities.Subscription", null)
                        .WithMany("Changes")
                        .HasForeignKey("SubscriptionId")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("SubscriptionManagement.Domain.Entities.SubscriptionPaymentProof", b =>
                {
                    b.HasOne("SubscriptionManagement.Domain.Entities.Subscription", "Subscription")
                        .WithMany()
                        .HasForeignKey("SubscriptionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.OwnsOne("SubscriptionManagement.Domain.ValueObjects.Money", "Amount", b1 =>
                        {
                            b1.Property<Guid>("SubscriptionPaymentProofId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(18,2)")
                                .HasColumnName("Amount");

                            b1.Property<string>("Currency")
                                .IsRequired()
                                .HasMaxLength(3)
                                .HasColumnType("character varying(3)")
                                .HasColumnName("Currency");

                            b1.HasKey("SubscriptionPaymentProofId");

                            b1.ToTable("SubscriptionPaymentProofs");

                            b1.WithOwner()
                                .HasForeignKey("SubscriptionPaymentProofId");
                        });

                    b.Navigation("Amount")
                        .IsRequired();

                    b.Navigation("Subscription");
                });

            modelBuilder.Entity("SubscriptionManagement.Domain.Entities.TaxCategory", b =>
                {
                    b.OwnsMany("SubscriptionManagement.Domain.ValueObjects.TaxConfiguration", "TaxConfigurations", b1 =>
                        {
                            b1.Property<Guid>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("uuid");

                            b1.Property<string>("ApplicableRegions")
                                .IsRequired()
                                .HasMaxLength(1000)
                                .HasColumnType("character varying(1000)")
                                .HasColumnName("ApplicableRegions");

                            b1.Property<DateTime>("EffectiveDate")
                                .HasColumnType("timestamp with time zone");

                            b1.Property<DateTime?>("ExpirationDate")
                                .HasColumnType("timestamp with time zone");

                            b1.Property<bool>("IsIncluded")
                                .HasColumnType("boolean");

                            b1.Property<decimal>("Rate")
                                .HasColumnType("decimal(5,4)");

                            b1.Property<Guid>("TaxCategoryId")
                                .HasColumnType("uuid");

                            b1.Property<string>("TaxType")
                                .IsRequired()
                                .HasColumnType("text");

                            b1.HasKey("Id");

                            b1.HasIndex("TaxCategoryId");

                            b1.ToTable("TaxCategoryConfigurations", (string)null);

                            b1.WithOwner()
                                .HasForeignKey("TaxCategoryId");
                        });

                    b.Navigation("TaxConfigurations");
                });

            modelBuilder.Entity("SubscriptionManagement.Domain.Entities.UsageRecord", b =>
                {
                    b.HasOne("SubscriptionManagement.Domain.Entities.Subscription", "Subscription")
                        .WithMany()
                        .HasForeignKey("SubscriptionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Subscription");
                });

            modelBuilder.Entity("SubscriptionManagement.Domain.Entities.FeatureFlag", b =>
                {
                    b.Navigation("Rules");

                    b.Navigation("UsageHistory");
                });

            modelBuilder.Entity("SubscriptionManagement.Domain.Entities.Plan", b =>
                {
                    b.Navigation("Features");

                    b.Navigation("PlanTaxConfigurations");
                });

            modelBuilder.Entity("SubscriptionManagement.Domain.Entities.Subscription", b =>
                {
                    b.Navigation("Changes");

                    b.Navigation("Payments");
                });
#pragma warning restore 612, 618
        }
    }
}
