using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.Interfaces;

namespace SubscriptionManagement.Application.Commands.UpdateTierConfigurationSetup
{
    public class UpdateTierConfigurationSetupCommandHandler : IRequestHandler<UpdateTierConfigurationSetupCommand, bool>
    {
        private readonly ITierConfigurationSetupRepository _repository;
        private readonly ILogger<UpdateTierConfigurationSetupCommandHandler> _logger;

        public UpdateTierConfigurationSetupCommandHandler(
            ITierConfigurationSetupRepository repository,
            ILogger<UpdateTierConfigurationSetupCommandHandler> logger)
        {
            _repository = repository;
            _logger = logger;
        }

        public async Task<bool> Handle(UpdateTierConfigurationSetupCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Updating tier configuration setup with ID: {Id}", request.Id);

                // Get existing entity
                var existingEntity = await _repository.GetByIdAsync(request.Id);
                if (existingEntity == null)
                {
                    _logger.LogWarning("Tier configuration setup with ID: {Id} not found", request.Id);
                    return false;
                }

                // Check if type already exists (excluding current entity)
                var existsByType = await _repository.ExistsByTypeAsync(request.Type, request.Id);
                if (existsByType)
                {
                    throw new InvalidOperationException($"Tier configuration setup with type '{request.Type}' already exists");
                }

                // Update the entity
                existingEntity.Update(
                    request.Type,
                    "Admin",
                    request.IsActive,
                    request.IsCustom);

                // Save changes
                await _repository.UpdateAsync(existingEntity);

                _logger.LogInformation("Successfully updated tier configuration setup with ID: {Id}", request.Id);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating tier configuration setup with ID: {Id}", request.Id);
                throw;
            }
        }
    }
}
