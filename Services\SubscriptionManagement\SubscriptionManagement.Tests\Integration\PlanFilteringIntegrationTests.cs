using Microsoft.EntityFrameworkCore;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.ValueObjects;
using SubscriptionManagement.Infrastructure.Persistence;
using SubscriptionManagement.Infrastructure.Repositories;
using Xunit;

namespace SubscriptionManagement.Tests.Integration;

public class PlanFilteringIntegrationTests : IDisposable
{
    private readonly SubscriptionDbContext _context;
    private readonly PlanRepository _repository;

    public PlanFilteringIntegrationTests()
    {
        var options = new DbContextOptionsBuilder<SubscriptionDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new SubscriptionDbContext(options);
        _repository = new PlanRepository(_context);

        SeedTestData();
    }

    [Fact]
    public async Task GetFilteredPlansAsync_FilterByUserType_ReturnsCorrectPlans()
    {
        // Act
        var (plans, totalCount) = await _repository.GetFilteredPlansAsync(1, 10, UserType.TransportCompany);

        // Assert
        Assert.Equal(2, totalCount);
        Assert.All(plans, p => Assert.Equal(UserType.TransportCompany, p.UserType));
    }

    [Fact]
    public async Task GetFilteredPlansAsync_FilterByIsActive_ReturnsCorrectPlans()
    {
        // Act
        var (activePlans, activeCount) = await _repository.GetFilteredPlansAsync(1, 10, isActive: true);
        var (inactivePlans, inactiveCount) = await _repository.GetFilteredPlansAsync(1, 10, isActive: false);

        // Assert
        Assert.Equal(4, activeCount);
        Assert.Equal(1, inactiveCount);
        Assert.All(activePlans, p => Assert.True(p.IsActive));
        Assert.All(inactivePlans, p => Assert.False(p.IsActive));
    }

    [Fact]
    public async Task GetFilteredPlansAsync_SearchByName_ReturnsCorrectPlans()
    {
        // Act
        var (plans, totalCount) = await _repository.GetFilteredPlansAsync(1, 10, searchTerm: "basic");

        // Assert
        Assert.Equal(2, totalCount);
        Assert.All(plans, p => Assert.Contains("basic", p.Name.ToLower()));
    }

    [Fact]
    public async Task GetFilteredPlansAsync_FilterByPlanType_ReturnsCorrectPlans()
    {
        // Act
        var (plans, totalCount) = await _repository.GetFilteredPlansAsync(1, 10, planType: PlanType.Pro);

        // Assert
        Assert.Equal(1, totalCount);
        Assert.All(plans, p => Assert.Equal(PlanType.Pro, p.Type));
    }

    [Fact]
    public async Task GetFilteredPlansAsync_FilterByTrialPeriod_ReturnsCorrectPlans()
    {
        // Act
        var (trialPlans, trialCount) = await _repository.GetFilteredPlansAsync(1, 10, hasTrialPeriod: true);
        var (nonTrialPlans, nonTrialCount) = await _repository.GetFilteredPlansAsync(1, 10, hasTrialPeriod: false);

        // Assert
        Assert.Equal(3, trialCount);
        Assert.Equal(2, nonTrialCount);
        Assert.All(trialPlans, p => Assert.True(p.TrialPeriodDays.HasValue && p.TrialPeriodDays.Value > 0));
        Assert.All(nonTrialPlans, p => Assert.True(!p.TrialPeriodDays.HasValue || p.TrialPeriodDays.Value <= 0));
    }

    [Fact]
    public async Task GetFilteredPlansAsync_MultipleFilters_ReturnsCorrectPlans()
    {
        // Act
        var (plans, totalCount) = await _repository.GetFilteredPlansAsync(
            1, 10, 
            UserType.TransportCompany, 
            true, 
            "basic", 
            PlanType.Basic, 
            true);

        // Assert
        Assert.Equal(1, totalCount);
        var plan = plans.First();
        Assert.Equal(UserType.TransportCompany, plan.UserType);
        Assert.True(plan.IsActive);
        Assert.Contains("basic", plan.Name.ToLower());
        Assert.Equal(PlanType.Basic, plan.Type);
        Assert.True(plan.TrialPeriodDays.HasValue && plan.TrialPeriodDays.Value > 0);
    }

    [Fact]
    public async Task GetFilteredPlansAsync_Pagination_ReturnsCorrectPage()
    {
        // Act
        var (firstPage, totalCount) = await _repository.GetFilteredPlansAsync(1, 2);
        var (secondPage, _) = await _repository.GetFilteredPlansAsync(2, 2);

        // Assert
        Assert.Equal(5, totalCount);
        Assert.Equal(2, firstPage.Count);
        Assert.Equal(2, secondPage.Count);
        Assert.NotEqual(firstPage.First().Id, secondPage.First().Id);
    }

    [Fact]
    public async Task GetFilteredPlansAsync_CaseInsensitiveSearch_ReturnsCorrectPlans()
    {
        // Act
        var (lowerCase, lowerCount) = await _repository.GetFilteredPlansAsync(1, 10, searchTerm: "basic");
        var (upperCase, upperCount) = await _repository.GetFilteredPlansAsync(1, 10, searchTerm: "BASIC");
        var (mixedCase, mixedCount) = await _repository.GetFilteredPlansAsync(1, 10, searchTerm: "BaSiC");

        // Assert
        Assert.Equal(lowerCount, upperCount);
        Assert.Equal(upperCount, mixedCount);
        Assert.Equal(lowerCase.Count, upperCase.Count);
        Assert.Equal(upperCase.Count, mixedCase.Count);
    }

    private void SeedTestData()
    {
        var billingCycle = new BillingCycle(BillingInterval.Monthly, 1);
        var price = new Money(99.99m, "USD");
        var limits = new PlanLimits(10, 5, 3, false, false, false, false, false, false);

        var plans = new List<Plan>
        {
            new Plan("Basic Transport Plan", "Basic plan for transport companies", "1.0", 
                PlanType.Basic, UserType.TransportCompany, price, billingCycle, limits, true, 30),
            new Plan("Basic Carrier Plan", "Basic plan for carriers", "1.0", 
                PlanType.Basic, UserType.Carrier, price, billingCycle, limits, true, null),
            new Plan("Pro Transport Plan", "Pro plan for transport companies", "1.0", 
                PlanType.Pro, UserType.TransportCompany, price, billingCycle, limits, true, 14),
            new Plan("Standard Broker Plan", "Standard plan for brokers", "1.0", 
                PlanType.Standard, UserType.Broker, price, billingCycle, limits, true, 7),
            new Plan("Inactive Plan", "Inactive plan", "1.0", 
                PlanType.Premium, UserType.Shipper, price, billingCycle, limits, false, null)
        };

        // Deactivate the last plan
        plans[4].Deactivate();

        _context.Plans.AddRange(plans);
        _context.SaveChanges();
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
