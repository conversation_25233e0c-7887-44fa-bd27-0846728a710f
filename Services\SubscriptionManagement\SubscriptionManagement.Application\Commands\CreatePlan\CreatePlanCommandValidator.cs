using FluentValidation;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Application.Commands.CreatePlan
{
    public class CreatePlanCommandValidator : AbstractValidator<CreatePlanCommand>
    {
        public CreatePlanCommandValidator()
        {
            RuleFor(x => x.Name)
                .NotEmpty()
                .WithMessage("Plan name is required")
                .MaximumLength(100)
                .WithMessage("Plan name cannot exceed 100 characters");

            RuleFor(x => x.Description)
                .NotEmpty()
                .WithMessage("Plan description is required")
                .MaximumLength(500)
                .WithMessage("Plan description cannot exceed 500 characters");

            RuleFor(x => x.Version)
                .GreaterThan(0)
                .WithMessage("Plan version must be greater than 0")
                .LessThanOrEqualTo(1000)
                .WithMessage("Plan version cannot exceed 1000");

            RuleFor(x => x.Type)
                .IsInEnum()
                .WithMessage("Invalid plan type");

            RuleFor(x => x.UserType)
                .IsInEnum()
                .WithMessage("Invalid user type");

            RuleFor(x => x.Price)
                .GreaterThan(0)
                .WithMessage("Plan price must be greater than zero");

            RuleFor(x => x.Currency)
                .NotEmpty()
                .WithMessage("Currency is required")
                .Length(3)
                .WithMessage("Currency must be a 3-character code (e.g., INR, USD)");

            RuleFor(x => x.BillingInterval)
                .IsInEnum()
                .WithMessage("Invalid billing interval");

            RuleFor(x => x.BillingIntervalCount)
                .GreaterThan(0)
                .WithMessage("Billing interval count must be greater than zero");

            // CustomBillingDays is only required and validated when BillingInterval is Custom
            RuleFor(x => x.CustomBillingDays)
                .NotNull()
                .WithMessage("Custom billing days is required when billing interval is Custom")
                .When(x => x.BillingInterval == BillingInterval.Custom);

            RuleFor(x => x.CustomBillingDays)
                .GreaterThan(0)
                .WithMessage("Custom billing days must be greater than zero")
                .When(x => x.BillingInterval == BillingInterval.Custom && x.CustomBillingDays.HasValue);

            RuleFor(x => x.TrialPeriodDays)
                .GreaterThanOrEqualTo(0)
                .WithMessage("Trial period days cannot be negative")
                .When(x => x.TrialPeriodDays.HasValue);

            RuleFor(x => x.SetupFee)
                .GreaterThanOrEqualTo(0)
                .WithMessage("Setup fee cannot be negative")
                .When(x => x.SetupFee.HasValue);

            RuleFor(x => x.SetupFeeCurrency)
                .Length(3)
                .WithMessage("Setup fee currency must be a 3-character code (e.g., INR, USD)")
                .When(x => !string.IsNullOrEmpty(x.SetupFeeCurrency));

            RuleFor(x => x.Limits)
                .NotNull()
                .WithMessage("Plan limits are required");

            RuleFor(x => x.Features)
                .NotNull()
                .WithMessage("Plan features list cannot be null")
                .Must(features => features.Count > 0)
                .WithMessage("Please select at least one feature.");

            // Validate individual features
            RuleForEach(x => x.Features).ChildRules(feature =>
            {
                feature.RuleFor(f => f.FeatureId)
                    .NotEmpty()
                    .WithMessage("Feature ID is required");

                feature.RuleFor(f => f.Name)
                    .NotEmpty()
                    .WithMessage("Feature name is required")
                    .MaximumLength(200)
                    .WithMessage("Feature name cannot exceed 200 characters");

                feature.RuleFor(f => f.Key)
                    .NotEmpty()
                    .WithMessage("Feature key is required")
                    .MaximumLength(100)
                    .WithMessage("Feature key cannot exceed 100 characters");

                feature.RuleFor(f => f.Description)
                    .MaximumLength(500)
                    .WithMessage("Feature description cannot exceed 500 characters")
                    .When(f => !string.IsNullOrEmpty(f.Description));
            });
        }
    }
}
