﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SubscriptionManagement.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreateWithBusinessCombinationConstraint : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "FeatureFlags",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    Key = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Type = table.Column<string>(type: "text", nullable: false),
                    Status = table.Column<string>(type: "text", nullable: false),
                    StartDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    EndDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RolloutPercentage = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TargetAudience = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    ABTestConfiguration = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    DefaultValue = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Variants = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    MenuId = table.Column<Guid>(type: "uuid", nullable: true),
                    Metadata = table.Column<string>(type: "jsonb", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FeatureFlags", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "GlobalTaxConfigurations",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TaxType = table.Column<string>(type: "text", nullable: false),
                    Rate = table.Column<decimal>(type: "numeric(5,4)", nullable: false),
                    IsIncluded = table.Column<bool>(type: "boolean", nullable: false),
                    ApplicableRegions = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    EffectiveDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ExpirationDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    Priority = table.Column<int>(type: "integer", nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: false),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_GlobalTaxConfigurations", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "NotificationHistories",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    SubscriptionId = table.Column<Guid>(type: "uuid", nullable: true),
                    UserId = table.Column<Guid>(type: "uuid", nullable: true),
                    Type = table.Column<int>(type: "integer", nullable: false),
                    Channel = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Subject = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    Body = table.Column<string>(type: "text", nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "Pending"),
                    ExternalNotificationId = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    TriggeredByUserId = table.Column<Guid>(type: "uuid", nullable: false),
                    ScheduledAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    SentAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeliveredAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ReadAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ClickedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ErrorMessage = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    ErrorCode = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    RetryCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    LastRetryAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Metadata = table.Column<string>(type: "jsonb", nullable: false),
                    DeliveryMetadata = table.Column<string>(type: "jsonb", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NotificationHistories", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "NotificationTemplates",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    Type = table.Column<int>(type: "integer", nullable: false),
                    Channel = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Language = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false, defaultValue: "en"),
                    Subject = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    Body = table.Column<string>(type: "text", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    Variables = table.Column<string>(type: "jsonb", nullable: false),
                    Metadata = table.Column<string>(type: "jsonb", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NotificationTemplates", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "TaxCategories",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    Code = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TaxCategories", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "TaxExemptions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    ExemptionType = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    ExemptionNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    IssuingAuthority = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    ValidFrom = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ValidTo = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ExemptTaxTypes = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    ApplicableRegions = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    DocumentPath = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    VerifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    VerifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    VerificationNotes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TaxExemptions", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "FeatureFlagRules",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    FeatureFlagId = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    Type = table.Column<string>(type: "text", nullable: false),
                    Condition = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    Priority = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FeatureFlagRules", x => x.Id);
                    table.ForeignKey(
                        name: "FK_FeatureFlagRules_FeatureFlags_FeatureFlagId",
                        column: x => x.FeatureFlagId,
                        principalTable: "FeatureFlags",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "FeatureFlagUsage",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    FeatureFlagId = table.Column<Guid>(type: "uuid", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    Variant = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    AccessedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Context = table.Column<string>(type: "jsonb", nullable: false),
                    UserAgent = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    IpAddress = table.Column<string>(type: "character varying(45)", maxLength: 45, nullable: true),
                    SessionId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FeatureFlagUsage", x => x.Id);
                    table.ForeignKey(
                        name: "FK_FeatureFlagUsage_FeatureFlags_FeatureFlagId",
                        column: x => x.FeatureFlagId,
                        principalTable: "FeatureFlags",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Plans",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    Version = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Type = table.Column<string>(type: "text", nullable: false),
                    UserType = table.Column<string>(type: "text", nullable: false),
                    Price = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: false),
                    Currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    BillingInterval = table.Column<string>(type: "text", nullable: false),
                    BillingIntervalCount = table.Column<int>(type: "integer", nullable: false),
                    CustomBillingDays = table.Column<int>(type: "integer", nullable: true),
                    RfqLimit = table.Column<int>(type: "integer", nullable: true),
                    VehicleLimit = table.Column<int>(type: "integer", nullable: true),
                    CarrierLimit = table.Column<int>(type: "integer", nullable: true),
                    IsUnlimited = table.Column<bool>(type: "boolean", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    IsPublic = table.Column<bool>(type: "boolean", nullable: false),
                    TrialPeriodDays = table.Column<int>(type: "INTEGER", nullable: true),
                    SetupFeeAmount = table.Column<int>(type: "integer", nullable: true),
                    SetupFeeCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: true),
                    TaxCategoryId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Plans", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Plans_TaxCategories_TaxCategoryId",
                        column: x => x.TaxCategoryId,
                        principalTable: "TaxCategories",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "TaxCategoryConfigurations",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    TaxType = table.Column<string>(type: "text", nullable: false),
                    Rate = table.Column<decimal>(type: "numeric(5,4)", nullable: false),
                    IsIncluded = table.Column<bool>(type: "boolean", nullable: false),
                    ApplicableRegions = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    EffectiveDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ExpirationDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    TaxCategoryId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TaxCategoryConfigurations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TaxCategoryConfigurations_TaxCategories_TaxCategoryId",
                        column: x => x.TaxCategoryId,
                        principalTable: "TaxCategories",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PlanFeatures",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    FeatureId = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Key = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    IsEnabled = table.Column<bool>(type: "boolean", nullable: false),
                    PlanId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PlanFeatures", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PlanFeatures_Plans_PlanId",
                        column: x => x.PlanId,
                        principalTable: "Plans",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PlanTaxConfigurations",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    PlanId = table.Column<Guid>(type: "uuid", nullable: false),
                    TaxType = table.Column<string>(type: "text", nullable: false),
                    Rate = table.Column<decimal>(type: "numeric(5,4)", nullable: false),
                    IsIncluded = table.Column<bool>(type: "boolean", nullable: false),
                    ApplicableRegions = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    TaxEffectiveDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    TaxExpirationDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    EffectiveFrom = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    EffectiveTo = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Notes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PlanTaxConfigurations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PlanTaxConfigurations_Plans_PlanId",
                        column: x => x.PlanId,
                        principalTable: "Plans",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Subscriptions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    PlanId = table.Column<Guid>(type: "uuid", nullable: false),
                    Status = table.Column<string>(type: "text", nullable: false),
                    StartDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    EndDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    NextBillingDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    TrialEndDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CancelledAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CancellationReason = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    AutoRenew = table.Column<bool>(type: "boolean", nullable: false),
                    CurrentPrice = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: false),
                    CurrentCurrency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    BillingInterval = table.Column<string>(type: "text", nullable: false),
                    BillingIntervalCount = table.Column<int>(type: "integer", nullable: false),
                    CustomBillingDays = table.Column<int>(type: "integer", nullable: true),
                    ProrationMode = table.Column<string>(type: "text", nullable: false),
                    GracePeriodEndDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastExtendedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ExtensionReason = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    ExtendedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    PaymentMethodId = table.Column<string>(type: "text", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Subscriptions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Subscriptions_Plans_PlanId",
                        column: x => x.PlanId,
                        principalTable: "Plans",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Payments",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    SubscriptionId = table.Column<Guid>(type: "uuid", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    Amount = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: false),
                    Currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    SubscriptionId1 = table.Column<Guid>(type: "uuid", nullable: true),
                    Status = table.Column<string>(type: "text", nullable: false),
                    PaymentGatewayTransactionId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    PaymentMethod = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    ProcessedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    FailedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    FailureReason = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    PaymentGatewayResponse = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    RetryCount = table.Column<int>(type: "integer", nullable: false),
                    NextRetryAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Payments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Payments_Subscriptions_SubscriptionId",
                        column: x => x.SubscriptionId,
                        principalTable: "Subscriptions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Payments_Subscriptions_SubscriptionId1",
                        column: x => x.SubscriptionId1,
                        principalTable: "Subscriptions",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "SubscriptionChanges",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ChangeType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    ChangedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    SubscriptionId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SubscriptionChanges", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SubscriptionChanges_Subscriptions_SubscriptionId",
                        column: x => x.SubscriptionId,
                        principalTable: "Subscriptions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "SubscriptionPaymentProofs",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    SubscriptionId = table.Column<Guid>(type: "uuid", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    Amount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Currency = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    PaymentDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ProofImageUrl = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: false),
                    Notes = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    PaymentMethod = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    TransactionReference = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    VerifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    VerifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    VerificationNotes = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    RejectionReason = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SubscriptionPaymentProofs", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SubscriptionPaymentProofs_Subscriptions_SubscriptionId",
                        column: x => x.SubscriptionId,
                        principalTable: "Subscriptions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "UsageRecords",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    SubscriptionId = table.Column<Guid>(type: "uuid", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    FeatureKey = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    UsageCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 1),
                    UsageDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Metadata = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    PeriodStart = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    PeriodEnd = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UsageRecords", x => x.Id);
                    table.ForeignKey(
                        name: "FK_UsageRecords_Subscriptions_SubscriptionId",
                        column: x => x.SubscriptionId,
                        principalTable: "Subscriptions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_FeatureFlagRules_FeatureFlagId_IsActive",
                table: "FeatureFlagRules",
                columns: new[] { "FeatureFlagId", "IsActive" });

            migrationBuilder.CreateIndex(
                name: "IX_FeatureFlagRules_IsActive",
                table: "FeatureFlagRules",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_FeatureFlagRules_Priority",
                table: "FeatureFlagRules",
                column: "Priority");

            migrationBuilder.CreateIndex(
                name: "IX_FeatureFlagRules_Type",
                table: "FeatureFlagRules",
                column: "Type");

            migrationBuilder.CreateIndex(
                name: "IX_FeatureFlags_Key",
                table: "FeatureFlags",
                column: "Key",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_FeatureFlags_MenuId",
                table: "FeatureFlags",
                column: "MenuId");

            migrationBuilder.CreateIndex(
                name: "IX_FeatureFlags_Name",
                table: "FeatureFlags",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_FeatureFlags_Status",
                table: "FeatureFlags",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_FeatureFlags_Type",
                table: "FeatureFlags",
                column: "Type");

            migrationBuilder.CreateIndex(
                name: "IX_FeatureFlagUsage_AccessedAt",
                table: "FeatureFlagUsage",
                column: "AccessedAt");

            migrationBuilder.CreateIndex(
                name: "IX_FeatureFlagUsage_FeatureFlagId_UserId",
                table: "FeatureFlagUsage",
                columns: new[] { "FeatureFlagId", "UserId" });

            migrationBuilder.CreateIndex(
                name: "IX_FeatureFlagUsage_FeatureFlagId_Variant",
                table: "FeatureFlagUsage",
                columns: new[] { "FeatureFlagId", "Variant" });

            migrationBuilder.CreateIndex(
                name: "IX_FeatureFlagUsage_SessionId",
                table: "FeatureFlagUsage",
                column: "SessionId");

            migrationBuilder.CreateIndex(
                name: "IX_FeatureFlagUsage_UserId",
                table: "FeatureFlagUsage",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_GlobalTaxConfigurations_CreatedAt",
                table: "GlobalTaxConfigurations",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_GlobalTaxConfigurations_CreatedByUserId",
                table: "GlobalTaxConfigurations",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_GlobalTaxConfigurations_IsActive",
                table: "GlobalTaxConfigurations",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_GlobalTaxConfigurations_IsActive_Priority",
                table: "GlobalTaxConfigurations",
                columns: new[] { "IsActive", "Priority" });

            migrationBuilder.CreateIndex(
                name: "IX_GlobalTaxConfigurations_Priority",
                table: "GlobalTaxConfigurations",
                column: "Priority");

            migrationBuilder.CreateIndex(
                name: "IX_NotificationHistories_Channel",
                table: "NotificationHistories",
                column: "Channel");

            migrationBuilder.CreateIndex(
                name: "IX_NotificationHistories_Channel_Status",
                table: "NotificationHistories",
                columns: new[] { "Channel", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_NotificationHistories_CreatedAt",
                table: "NotificationHistories",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_NotificationHistories_DeliveredAt",
                table: "NotificationHistories",
                column: "DeliveredAt");

            migrationBuilder.CreateIndex(
                name: "IX_NotificationHistories_ExternalNotificationId",
                table: "NotificationHistories",
                column: "ExternalNotificationId");

            migrationBuilder.CreateIndex(
                name: "IX_NotificationHistories_ScheduledAt",
                table: "NotificationHistories",
                column: "ScheduledAt");

            migrationBuilder.CreateIndex(
                name: "IX_NotificationHistories_SentAt",
                table: "NotificationHistories",
                column: "SentAt");

            migrationBuilder.CreateIndex(
                name: "IX_NotificationHistories_Status",
                table: "NotificationHistories",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_NotificationHistories_Status_CreatedAt",
                table: "NotificationHistories",
                columns: new[] { "Status", "CreatedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_NotificationHistories_Status_RetryCount_LastRetryAt",
                table: "NotificationHistories",
                columns: new[] { "Status", "RetryCount", "LastRetryAt" });

            migrationBuilder.CreateIndex(
                name: "IX_NotificationHistories_SubscriptionId",
                table: "NotificationHistories",
                column: "SubscriptionId");

            migrationBuilder.CreateIndex(
                name: "IX_NotificationHistories_SubscriptionId_Type_CreatedAt",
                table: "NotificationHistories",
                columns: new[] { "SubscriptionId", "Type", "CreatedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_NotificationHistories_TriggeredByUserId",
                table: "NotificationHistories",
                column: "TriggeredByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_NotificationHistories_Type",
                table: "NotificationHistories",
                column: "Type");

            migrationBuilder.CreateIndex(
                name: "IX_NotificationHistories_Type_Status",
                table: "NotificationHistories",
                columns: new[] { "Type", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_NotificationHistories_UserId",
                table: "NotificationHistories",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_NotificationHistories_UserId_Type_CreatedAt",
                table: "NotificationHistories",
                columns: new[] { "UserId", "Type", "CreatedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_NotificationTemplates_Channel",
                table: "NotificationTemplates",
                column: "Channel");

            migrationBuilder.CreateIndex(
                name: "IX_NotificationTemplates_Channel_IsActive",
                table: "NotificationTemplates",
                columns: new[] { "Channel", "IsActive" });

            migrationBuilder.CreateIndex(
                name: "IX_NotificationTemplates_IsActive",
                table: "NotificationTemplates",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_NotificationTemplates_Language",
                table: "NotificationTemplates",
                column: "Language");

            migrationBuilder.CreateIndex(
                name: "IX_NotificationTemplates_Name",
                table: "NotificationTemplates",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_NotificationTemplates_Type",
                table: "NotificationTemplates",
                column: "Type");

            migrationBuilder.CreateIndex(
                name: "IX_NotificationTemplates_Type_Channel_Language_IsActive",
                table: "NotificationTemplates",
                columns: new[] { "Type", "Channel", "Language", "IsActive" });

            migrationBuilder.CreateIndex(
                name: "IX_NotificationTemplates_Type_IsActive",
                table: "NotificationTemplates",
                columns: new[] { "Type", "IsActive" });

            migrationBuilder.CreateIndex(
                name: "IX_Payments_PaymentGatewayTransactionId",
                table: "Payments",
                column: "PaymentGatewayTransactionId");

            migrationBuilder.CreateIndex(
                name: "IX_Payments_ProcessedAt",
                table: "Payments",
                column: "ProcessedAt");

            migrationBuilder.CreateIndex(
                name: "IX_Payments_Status",
                table: "Payments",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_Payments_SubscriptionId",
                table: "Payments",
                column: "SubscriptionId");

            migrationBuilder.CreateIndex(
                name: "IX_Payments_SubscriptionId1",
                table: "Payments",
                column: "SubscriptionId1");

            migrationBuilder.CreateIndex(
                name: "IX_Payments_UserId",
                table: "Payments",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_PlanFeatures_Key",
                table: "PlanFeatures",
                column: "Key");

            migrationBuilder.CreateIndex(
                name: "IX_PlanFeatures_PlanId_FeatureId",
                table: "PlanFeatures",
                columns: new[] { "PlanId", "FeatureId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Plans_IsActive",
                table: "Plans",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_Plans_IsPublic",
                table: "Plans",
                column: "IsPublic");

            migrationBuilder.CreateIndex(
                name: "IX_Plans_Name",
                table: "Plans",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_Plans_TaxCategoryId",
                table: "Plans",
                column: "TaxCategoryId");

            migrationBuilder.CreateIndex(
                name: "IX_Plans_UserType_Type",
                table: "Plans",
                columns: new[] { "UserType", "Type" });

            // Create unique constraint on business combination (UserType + PlanType + BillingCycle)
            migrationBuilder.CreateIndex(
                name: "IX_Plans_BusinessCombination",
                table: "Plans",
                columns: new[] { "UserType", "Type", "BillingInterval", "BillingIntervalCount", "CustomBillingDays" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PlanTaxConfigurations_CreatedAt",
                table: "PlanTaxConfigurations",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_PlanTaxConfigurations_EffectiveFrom",
                table: "PlanTaxConfigurations",
                column: "EffectiveFrom");

            migrationBuilder.CreateIndex(
                name: "IX_PlanTaxConfigurations_EffectiveTo",
                table: "PlanTaxConfigurations",
                column: "EffectiveTo");

            migrationBuilder.CreateIndex(
                name: "IX_PlanTaxConfigurations_IsActive",
                table: "PlanTaxConfigurations",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_PlanTaxConfigurations_PlanId",
                table: "PlanTaxConfigurations",
                column: "PlanId");

            migrationBuilder.CreateIndex(
                name: "IX_PlanTaxConfigurations_PlanId_EffectiveFrom_EffectiveTo",
                table: "PlanTaxConfigurations",
                columns: new[] { "PlanId", "EffectiveFrom", "EffectiveTo" });

            migrationBuilder.CreateIndex(
                name: "IX_PlanTaxConfigurations_PlanId_IsActive",
                table: "PlanTaxConfigurations",
                columns: new[] { "PlanId", "IsActive" });

            migrationBuilder.CreateIndex(
                name: "IX_SubscriptionChanges_ChangedAt",
                table: "SubscriptionChanges",
                column: "ChangedAt");

            migrationBuilder.CreateIndex(
                name: "IX_SubscriptionChanges_SubscriptionId",
                table: "SubscriptionChanges",
                column: "SubscriptionId");

            migrationBuilder.CreateIndex(
                name: "IX_SubscriptionPaymentProofs_CreatedAt",
                table: "SubscriptionPaymentProofs",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_SubscriptionPaymentProofs_PaymentDate",
                table: "SubscriptionPaymentProofs",
                column: "PaymentDate");

            migrationBuilder.CreateIndex(
                name: "IX_SubscriptionPaymentProofs_Status",
                table: "SubscriptionPaymentProofs",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_SubscriptionPaymentProofs_Status_CreatedAt",
                table: "SubscriptionPaymentProofs",
                columns: new[] { "Status", "CreatedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_SubscriptionPaymentProofs_SubscriptionId",
                table: "SubscriptionPaymentProofs",
                column: "SubscriptionId");

            migrationBuilder.CreateIndex(
                name: "IX_SubscriptionPaymentProofs_SubscriptionId_Status",
                table: "SubscriptionPaymentProofs",
                columns: new[] { "SubscriptionId", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_SubscriptionPaymentProofs_UserId",
                table: "SubscriptionPaymentProofs",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_SubscriptionPaymentProofs_UserId_Status",
                table: "SubscriptionPaymentProofs",
                columns: new[] { "UserId", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_SubscriptionPaymentProofs_VerifiedAt",
                table: "SubscriptionPaymentProofs",
                column: "VerifiedAt");

            migrationBuilder.CreateIndex(
                name: "IX_Subscriptions_ExtendedByUserId",
                table: "Subscriptions",
                column: "ExtendedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_Subscriptions_GracePeriodEndDate",
                table: "Subscriptions",
                column: "GracePeriodEndDate");

            migrationBuilder.CreateIndex(
                name: "IX_Subscriptions_LastExtendedAt",
                table: "Subscriptions",
                column: "LastExtendedAt");

            migrationBuilder.CreateIndex(
                name: "IX_Subscriptions_NextBillingDate",
                table: "Subscriptions",
                column: "NextBillingDate");

            migrationBuilder.CreateIndex(
                name: "IX_Subscriptions_PlanId",
                table: "Subscriptions",
                column: "PlanId");

            migrationBuilder.CreateIndex(
                name: "IX_Subscriptions_Status",
                table: "Subscriptions",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_Subscriptions_UserId",
                table: "Subscriptions",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_Subscriptions_UserId_Status",
                table: "Subscriptions",
                columns: new[] { "UserId", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_TaxCategories_Code",
                table: "TaxCategories",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_TaxCategories_CreatedAt",
                table: "TaxCategories",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_TaxCategories_IsActive",
                table: "TaxCategories",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_TaxCategories_Name",
                table: "TaxCategories",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_TaxCategoryConfigurations_TaxCategoryId",
                table: "TaxCategoryConfigurations",
                column: "TaxCategoryId");

            migrationBuilder.CreateIndex(
                name: "IX_TaxExemptions_CreatedAt",
                table: "TaxExemptions",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_TaxExemptions_ExemptionNumber",
                table: "TaxExemptions",
                column: "ExemptionNumber");

            migrationBuilder.CreateIndex(
                name: "IX_TaxExemptions_ExemptionNumber_UserId",
                table: "TaxExemptions",
                columns: new[] { "ExemptionNumber", "UserId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_TaxExemptions_IsActive",
                table: "TaxExemptions",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_TaxExemptions_UserId",
                table: "TaxExemptions",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_TaxExemptions_UserId_IsActive",
                table: "TaxExemptions",
                columns: new[] { "UserId", "IsActive" });

            migrationBuilder.CreateIndex(
                name: "IX_TaxExemptions_ValidFrom",
                table: "TaxExemptions",
                column: "ValidFrom");

            migrationBuilder.CreateIndex(
                name: "IX_TaxExemptions_ValidTo",
                table: "TaxExemptions",
                column: "ValidTo");

            migrationBuilder.CreateIndex(
                name: "IX_TaxExemptions_VerifiedAt",
                table: "TaxExemptions",
                column: "VerifiedAt");

            migrationBuilder.CreateIndex(
                name: "IX_UsageRecords_FeatureKey",
                table: "UsageRecords",
                column: "FeatureKey");

            migrationBuilder.CreateIndex(
                name: "IX_UsageRecords_SubscriptionId",
                table: "UsageRecords",
                column: "SubscriptionId");

            migrationBuilder.CreateIndex(
                name: "IX_UsageRecords_SubscriptionId_FeatureKey_PeriodStart_PeriodEnd",
                table: "UsageRecords",
                columns: new[] { "SubscriptionId", "FeatureKey", "PeriodStart", "PeriodEnd" });

            migrationBuilder.CreateIndex(
                name: "IX_UsageRecords_UsageDate",
                table: "UsageRecords",
                column: "UsageDate");

            migrationBuilder.CreateIndex(
                name: "IX_UsageRecords_UserId",
                table: "UsageRecords",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_UsageRecords_UserId_FeatureKey_PeriodStart_PeriodEnd",
                table: "UsageRecords",
                columns: new[] { "UserId", "FeatureKey", "PeriodStart", "PeriodEnd" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "FeatureFlagRules");

            migrationBuilder.DropTable(
                name: "FeatureFlagUsage");

            migrationBuilder.DropTable(
                name: "GlobalTaxConfigurations");

            migrationBuilder.DropTable(
                name: "NotificationHistories");

            migrationBuilder.DropTable(
                name: "NotificationTemplates");

            migrationBuilder.DropTable(
                name: "Payments");

            migrationBuilder.DropTable(
                name: "PlanFeatures");

            migrationBuilder.DropTable(
                name: "PlanTaxConfigurations");

            migrationBuilder.DropTable(
                name: "SubscriptionChanges");

            migrationBuilder.DropTable(
                name: "SubscriptionPaymentProofs");

            migrationBuilder.DropTable(
                name: "TaxCategoryConfigurations");

            migrationBuilder.DropTable(
                name: "TaxExemptions");

            migrationBuilder.DropTable(
                name: "UsageRecords");

            migrationBuilder.DropTable(
                name: "FeatureFlags");

            migrationBuilder.DropTable(
                name: "Subscriptions");

            migrationBuilder.DropTable(
                name: "Plans");

            migrationBuilder.DropTable(
                name: "TaxCategories");
        }
    }
}
