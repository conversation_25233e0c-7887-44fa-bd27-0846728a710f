using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using UserManagement.Domain.Repositories;
using UserManagement.Infrastructure.Data;
using UserManagement.Infrastructure.Repositories;
using UserManagement.Application.Services;
using UserManagement.Infrastructure.Services;
using UserManagement.Application.Interfaces;
using UserManagement.Infrastructure.Hubs;
using Microsoft.AspNetCore.SignalR;

namespace UserManagement.Infrastructure
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
        {
            // Add Entity Framework
            services.AddDbContext<UserManagementDbContext>(options =>
                options.UseNpgsql(configuration.GetConnectionString("DefaultConnection"),
                    b => b.MigrationsAssembly(typeof(UserManagementDbContext).Assembly.FullName)));

            // Add repositories
            services.AddScoped<IUserProfileRepository, UserProfileRepository>();
            services.AddScoped<IDocumentSubmissionRepository, DocumentSubmissionRepository>();
            services.AddScoped<IAuditLogRepository, AuditLogRepository>();
            services.AddScoped<ITransportCompanyLogoRepository, TransportCompanyLogoRepository>();

            // Add missing repositories with stub implementations
            services.AddScoped<UserManagement.Application.Interfaces.ITransportCompanyRepository, StubTransportCompanyRepository>();
            services.AddScoped<UserManagement.Domain.Repositories.IKycApprovalHistoryRepository, StubKycApprovalHistoryRepository>();

            // Add services
            services.AddScoped<IAuditService, AuditService>();
            services.AddScoped<UserManagement.Application.Services.INotificationService, SignalRNotificationService>();
            services.AddScoped<UserManagement.Application.Services.IOcrService, AzureOcrService>();
            services.AddScoped<UserManagement.Application.Interfaces.IOCRService, AzureOcrServiceAdapter>();
            services.AddScoped<UserManagement.Application.Services.IFileStorageService, FileStorageService>();
            services.AddScoped<UserManagement.Application.Interfaces.IImageProcessingService, StubImageProcessingService>();

            // Add background services
            services.AddHostedService<UserManagement.Infrastructure.Services.UserProfileMessageConsumer>();

            // Add missing stub services
            services.AddScoped<UserManagement.Application.Interfaces.IDocumentValidationService, StubDocumentValidationService>();
            services.AddScoped<UserManagement.Application.Interfaces.IFileStorageService, StubFileStorageServiceAdapter>();
            services.AddScoped<UserManagement.Application.Interfaces.ILanguageDetectionService, StubLanguageDetectionService>();
            services.AddScoped<UserManagement.Application.Interfaces.INotificationService, StubNotificationServiceAdapter>();
            services.AddScoped<UserManagement.Application.Interfaces.ITranslationService, StubTranslationService>();
            services.AddScoped<UserManagement.Application.Interfaces.ILocalizationService, StubLocalizationService>();

            // Add folder access service for document security
            services.AddScoped<UserManagement.Application.Interfaces.IFolderAccessService, FolderAccessService>();

            // Add HttpClient for OCR service
            services.AddHttpClient<AzureOcrService>();

            // Add HttpClient for location services (Geoapify API)
            services.AddHttpClient("GeoapifyClient", client =>
            {
                client.Timeout = TimeSpan.FromSeconds(30);
                client.DefaultRequestHeaders.Add("User-Agent", "TLI-UserManagement/1.0");
            });

            // Add HttpClient for Setu PAN verification API
            services.AddHttpClient("SetuClient", client =>
            {
                client.Timeout = TimeSpan.FromSeconds(30);
                client.DefaultRequestHeaders.Add("User-Agent", "TLI-UserManagement/1.0");
            });

            // Add SignalR
            services.AddSignalR();

            return services;
        }
    }
}
