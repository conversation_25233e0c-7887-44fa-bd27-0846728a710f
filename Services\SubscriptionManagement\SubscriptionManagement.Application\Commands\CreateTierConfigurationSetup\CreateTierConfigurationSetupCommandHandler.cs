using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Entities;

namespace SubscriptionManagement.Application.Commands.CreateTierConfigurationSetup
{
    public class CreateTierConfigurationSetupCommandHandler : IRequestHandler<CreateTierConfigurationSetupCommand, Guid>
    {
        private readonly ITierConfigurationSetupRepository _repository;
        private readonly ILogger<CreateTierConfigurationSetupCommandHandler> _logger;

        public CreateTierConfigurationSetupCommandHandler(
            ITierConfigurationSetupRepository repository,
            ILogger<CreateTierConfigurationSetupCommandHandler> logger)
        {
            _repository = repository;
            _logger = logger;
        }

        public async Task<Guid> Handle(CreateTierConfigurationSetupCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Creating tier configuration setup with type: {Type}", request.Type);

                // Check if type already exists
                var existsByType = await _repository.ExistsByTypeAsync(request.Type);
                if (existsByType)
                {
                    throw new InvalidOperationException($"Tier configuration setup with type '{request.Type}' already exists");
                }

                // Create the entity
                var tierConfigurationSetup = new TierConfigurationSetup(
                    request.Type,
                    "Admin",
                    request.IsActive,
                    request.IsCustom);

                // Save to repository
                var result = await _repository.AddAsync(tierConfigurationSetup);

                _logger.LogInformation("Successfully created tier configuration setup with ID: {Id}", result.Id);

                return result.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating tier configuration setup with type: {Type}", request.Type);
                throw;
            }
        }
    }
}
