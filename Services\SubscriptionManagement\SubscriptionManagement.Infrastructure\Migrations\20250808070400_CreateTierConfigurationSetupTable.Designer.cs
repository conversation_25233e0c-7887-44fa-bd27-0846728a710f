// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using SubscriptionManagement.Infrastructure.Persistence;

#nullable disable

namespace SubscriptionManagement.Infrastructure.Migrations
{
    [DbContext(typeof(SubscriptionDbContext))]
    [Migration("20250808070400_CreateTierConfigurationSetupTable")]
    partial class CreateTierConfigurationSetupTable
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
            // This is a minimal migration that only creates the TierConfigurationSetup table
            // The full model is maintained in the ModelSnapshot
        }
    }
}
