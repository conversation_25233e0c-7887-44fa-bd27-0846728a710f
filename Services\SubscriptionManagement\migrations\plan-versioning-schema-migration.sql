-- =====================================================
-- Plan Versioning Schema Migration
-- Converts from unique-ID-per-version to same-ID-different-versions
-- =====================================================

BEGIN TRANSACTION;

-- Step 1: Create backup tables
SELECT * INTO Plans_Backup FROM Plans;
SELECT * INTO Subscriptions_Backup FROM Subscriptions;

-- Step 2: Drop existing foreign key constraints
IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_Subscriptions_Plans_PlanId')
    ALTER TABLE Subscriptions DROP CONSTRAINT FK_Subscriptions_Plans_PlanId;

-- Step 3: Drop existing primary key on Plans
ALTER TABLE Plans DROP CONSTRAINT PK_Plans;

-- Step 4: Create temporary mapping table to group plan versions by logical plan
CREATE TABLE #PlanVersionMapping (
    LogicalPlanId UNIQUEIDENTIFIER,
    OriginalPlanId UNIQUEIDENTIFIER,
    Version INT,
    PlanName NVARCHAR(255),
    UserType INT,
    PlanType INT
);

-- Step 5: Group plans by logical identity (Name + UserType + PlanType)
-- This identifies which plans are actually versions of the same logical plan
WITH PlanGroups AS (
    SELECT 
        Name,
        UserType,
        Type as PlanType,
        MIN(CreatedAt) as FirstCreated,
        ROW_NUMBER() OVER (ORDER BY MIN(CreatedAt)) as GroupNumber
    FROM Plans
    GROUP BY Name, UserType, Type
),
PlanVersions AS (
    SELECT 
        p.Id as OriginalPlanId,
        p.Name,
        p.UserType,
        p.Type as PlanType,
        p.Version,
        p.CreatedAt,
        pg.GroupNumber,
        ROW_NUMBER() OVER (PARTITION BY pg.GroupNumber ORDER BY p.CreatedAt, p.Version) as NewVersion
    FROM Plans p
    INNER JOIN PlanGroups pg ON p.Name = pg.Name AND p.UserType = pg.UserType AND p.Type = pg.PlanType
)
INSERT INTO #PlanVersionMapping (LogicalPlanId, OriginalPlanId, Version, PlanName, UserType, PlanType)
SELECT 
    NEWID() as LogicalPlanId,  -- Generate new logical plan ID for each group
    OriginalPlanId,
    NewVersion as Version,
    Name,
    UserType,
    PlanType
FROM PlanVersions;

-- Step 6: Update Plans table with new logical IDs and versions
UPDATE p SET 
    Id = pvm.LogicalPlanId,
    Version = pvm.Version
FROM Plans p
INNER JOIN #PlanVersionMapping pvm ON p.Id = pvm.OriginalPlanId;

-- Step 7: Create new composite primary key
ALTER TABLE Plans ADD CONSTRAINT PK_Plans PRIMARY KEY (Id, Version);

-- Step 8: Update Subscriptions table to reference the new logical plan IDs
UPDATE s SET 
    PlanId = pvm.LogicalPlanId,
    PlanVersion = pvm.Version
FROM Subscriptions s
INNER JOIN #PlanVersionMapping pvm ON s.PlanId = pvm.OriginalPlanId;

-- Step 9: Add PlanVersion column to Subscriptions if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Subscriptions') AND name = 'PlanVersion')
    ALTER TABLE Subscriptions ADD PlanVersion INT NOT NULL DEFAULT 1;

-- Step 10: Create new foreign key constraint with composite key
ALTER TABLE Subscriptions 
ADD CONSTRAINT FK_Subscriptions_Plans_PlanId_Version 
FOREIGN KEY (PlanId, PlanVersion) REFERENCES Plans(Id, Version);

-- Step 11: Create indexes for performance
CREATE INDEX IX_Plans_Id ON Plans(Id);
CREATE INDEX IX_Plans_Version ON Plans(Version);
CREATE INDEX IX_Plans_UserType_Type ON Plans(UserType, Type);
CREATE INDEX IX_Subscriptions_PlanId_Version ON Subscriptions(PlanId, PlanVersion);

-- Step 12: Update PlanFeatures table to use composite foreign key
IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_PlanFeatures_Plans_PlanId')
    ALTER TABLE PlanFeatures DROP CONSTRAINT FK_PlanFeatures_Plans_PlanId;

-- Add PlanVersion column to PlanFeatures if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('PlanFeatures') AND name = 'PlanVersion')
    ALTER TABLE PlanFeatures ADD PlanVersion INT NOT NULL DEFAULT 1;

-- Update PlanFeatures with correct plan versions
UPDATE pf SET 
    PlanId = pvm.LogicalPlanId,
    PlanVersion = pvm.Version
FROM PlanFeatures pf
INNER JOIN #PlanVersionMapping pvm ON pf.PlanId = pvm.OriginalPlanId;

-- Create new foreign key for PlanFeatures
ALTER TABLE PlanFeatures 
ADD CONSTRAINT FK_PlanFeatures_Plans_PlanId_Version 
FOREIGN KEY (PlanId, PlanVersion) REFERENCES Plans(Id, Version);

-- Step 13: Clean up temporary table
DROP TABLE #PlanVersionMapping;

-- Step 14: Verify data integrity
SELECT 
    'Plans' as TableName,
    COUNT(*) as RecordCount,
    COUNT(DISTINCT Id) as UniqueLogicalPlans,
    MAX(Version) as MaxVersion
FROM Plans
UNION ALL
SELECT 
    'Subscriptions' as TableName,
    COUNT(*) as RecordCount,
    COUNT(DISTINCT PlanId) as UniqueLogicalPlans,
    MAX(PlanVersion) as MaxVersion
FROM Subscriptions;

COMMIT TRANSACTION;

PRINT 'Plan versioning schema migration completed successfully!';
