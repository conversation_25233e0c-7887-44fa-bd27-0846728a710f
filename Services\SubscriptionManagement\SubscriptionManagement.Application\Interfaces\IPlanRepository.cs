using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.ValueObjects;

namespace SubscriptionManagement.Application.Interfaces
{
    public interface IPlanRepository
    {
        Task<Plan?> GetByIdAsync(Guid id);
        Task<Plan?> GetByNameAsync(string name);
        Task<List<Plan>> GetAllAsync();
        Task<List<Plan>> GetActiveAsync();
        Task<List<Plan>> GetPublicAsync();
        Task<List<Plan>> GetByUserTypeAsync(UserType userType);
        Task<List<Plan>> GetByTypeAsync(PlanType type);
        Task<List<Plan>> GetByUserTypeAndTypeAsync(UserType userType, PlanType type);
        Task<Plan?> GetByBusinessCombinationAsync(UserType userType, PlanType planType, BillingCycle billingCycle);
        Task<List<Plan>> GetPlansAsync(int page, int pageSize);
        Task<int> GetPlansCountAsync();
        Task<(List<Plan> Plans, int TotalCount)> GetFilteredPlansAsync(
            int page,
            int pageSize,
            UserType? userType = null,
            bool? isActive = null,
            string? searchTerm = null,
            PlanType? planType = null);
        Task AddAsync(Plan plan);
        Task UpdateAsync(Plan plan);
        Task DeleteAsync(Plan plan);
        Task<bool> ExistsAsync(Guid id);
        Task<bool> ExistsByNameAsync(string name);

        // Plan versioning methods (using logical PlanId)
        Task<Plan?> GetByPlanIdAndVersionAsync(Guid planId, int version);
        Task<bool> IsVersionInUseAsync(Guid planVersionId); // Uses unique plan version Id
        Task<int> GetActiveSubscriberCountAsync(Guid planVersionId); // Uses unique plan version Id
        Task<Dictionary<UserType, int>> GetSubscriberCountByUserTypeAsync(Guid planVersionId); // Uses unique plan version Id

        Task<int> GetHighestVersionAsync(Guid planId); // Uses logical PlanId
        Task<List<Plan>> GetAllVersionsAsync(Guid planId); // Uses logical PlanId

        // Legacy methods (for backward compatibility)
        Task<Plan?> GetByIdAndVersionAsync(Guid planId, int version);
        Task<bool> IsVersionInUseAsync(Guid planId, int version);
        Task<int> GetActiveSubscriberCountAsync(Guid planId, int version);
        Task<Dictionary<UserType, int>> GetSubscriberCountByUserTypeAsync(Guid planId, int version);
    }

}
