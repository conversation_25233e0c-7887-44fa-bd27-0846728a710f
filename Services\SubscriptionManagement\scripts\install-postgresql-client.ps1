# PostgreSQL Client Installation Script
# Description: Installs PostgreSQL client tools using Chocolatey or direct download
# Date: 2025-08-07

Write-Host "=== PostgreSQL Client Installation Script ===" -ForegroundColor Green
Write-Host "Date: $(Get-Date)" -ForegroundColor Gray
Write-Host ""

# Check if running as administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Warning "This script should be run as Administrator for best results."
    Write-Host "Attempting to install anyway..." -ForegroundColor Yellow
    Write-Host ""
}

# Method 1: Try Chocolatey first (recommended)
Write-Host "Checking for Chocolatey..." -ForegroundColor Yellow
try {
    $chocoVersion = choco --version
    Write-Host "Chocolatey found: $chocoVersion" -ForegroundColor Green
    
    Write-Host "Installing PostgreSQL client tools via Chocolatey..." -ForegroundColor Yellow
    choco install postgresql --params '/Password:postgres' -y
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "PostgreSQL installed successfully via Chocolatey!" -ForegroundColor Green
        Write-Host "Please restart your PowerShell session to use psql command." -ForegroundColor Yellow
        exit 0
    } else {
        Write-Warning "Chocolatey installation failed. Trying alternative method..."
    }
} catch {
    Write-Host "Chocolatey not found. Trying alternative installation method..." -ForegroundColor Yellow
}

# Method 2: Direct download and install
Write-Host ""
Write-Host "Downloading PostgreSQL installer..." -ForegroundColor Yellow

$downloadUrl = "https://get.enterprisedb.com/postgresql/postgresql-15.4-1-windows-x64.exe"
$installerPath = "$env:TEMP\postgresql-installer.exe"

try {
    # Download PostgreSQL installer
    Invoke-WebRequest -Uri $downloadUrl -OutFile $installerPath -UseBasicParsing
    Write-Host "Download completed: $installerPath" -ForegroundColor Green
    
    # Run installer in silent mode
    Write-Host "Running PostgreSQL installer..." -ForegroundColor Yellow
    Write-Host "This may take a few minutes..." -ForegroundColor Gray
    
    $installArgs = @(
        "--mode", "unattended",
        "--unattendedmodeui", "none",
        "--superpassword", "postgres",
        "--enable-components", "commandlinetools"
    )
    
    Start-Process -FilePath $installerPath -ArgumentList $installArgs -Wait -NoNewWindow
    
    # Add PostgreSQL to PATH
    $pgPath = "C:\Program Files\PostgreSQL\15\bin"
    if (Test-Path $pgPath) {
        $currentPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")
        if ($currentPath -notlike "*$pgPath*") {
            [Environment]::SetEnvironmentVariable("PATH", "$currentPath;$pgPath", "Machine")
            Write-Host "Added PostgreSQL to system PATH" -ForegroundColor Green
        }
    }
    
    Write-Host ""
    Write-Host "PostgreSQL installation completed!" -ForegroundColor Green
    Write-Host "Please restart your PowerShell session to use psql command." -ForegroundColor Yellow
    
} catch {
    Write-Error "Failed to download or install PostgreSQL: $($_.Exception.Message)"
    Write-Host ""
    Write-Host "Manual Installation Instructions:" -ForegroundColor Yellow
    Write-Host "1. Download PostgreSQL from: https://www.postgresql.org/download/windows/" -ForegroundColor Gray
    Write-Host "2. Run the installer and select 'Command Line Tools' component" -ForegroundColor Gray
    Write-Host "3. Add PostgreSQL bin directory to your PATH environment variable" -ForegroundColor Gray
    Write-Host "4. Restart PowerShell and run the migration script again" -ForegroundColor Gray
} finally {
    # Clean up installer file
    if (Test-Path $installerPath) {
        Remove-Item $installerPath -Force -ErrorAction SilentlyContinue
    }
}

Write-Host ""
Write-Host "Installation script completed." -ForegroundColor Green
