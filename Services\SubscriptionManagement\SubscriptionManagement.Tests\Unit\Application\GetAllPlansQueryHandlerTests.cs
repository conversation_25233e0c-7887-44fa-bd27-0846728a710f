using AutoMapper;
using Microsoft.Extensions.Logging;
using Moq;
using SubscriptionManagement.Application.DTOs;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Application.Queries.GetAllPlans;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.ValueObjects;
using Xunit;

namespace SubscriptionManagement.Tests.Unit.Application;

public class GetAllPlansQueryHandlerTests
{
    private readonly Mock<IPlanRepository> _mockPlanRepository;
    private readonly Mock<IMapper> _mockMapper;
    private readonly Mock<ILogger<GetAllPlansQueryHandler>> _mockLogger;
    private readonly GetAllPlansQueryHandler _handler;

    public GetAllPlansQueryHandlerTests()
    {
        _mockPlanRepository = new Mock<IPlanRepository>();
        _mockMapper = new Mock<IMapper>();
        _mockLogger = new Mock<ILogger<GetAllPlansQueryHandler>>();
        _handler = new GetAllPlansQueryHandler(_mockPlanRepository.Object, _mockMapper.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task Handle_ValidQuery_ReturnsFilteredPlans()
    {
        // Arrange
        var query = new GetAllPlansQuery(1, 10, UserType.TransportCompany, true, "Basic", PlanType.Basic, true);
        var plans = CreateTestPlans();
        var planDtos = CreateTestPlanDtos();

        _mockPlanRepository.Setup(x => x.GetFilteredPlansAsync(
            It.IsAny<int>(), It.IsAny<int>(), It.IsAny<UserType?>(), It.IsAny<bool?>(),
            It.IsAny<string>(), It.IsAny<PlanType?>(), It.IsAny<bool?>()))
            .ReturnsAsync((plans, 2));

        _mockMapper.Setup(x => x.Map<List<PlanDto>>(It.IsAny<List<Plan>>()))
            .Returns(planDtos);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Plans.Count);
        Assert.Equal(2, result.TotalCount);
        Assert.Equal(1, result.Page);
        Assert.Equal(10, result.PageSize);
        Assert.Equal(UserType.TransportCompany, result.Filters.UserType);
        Assert.True(result.Filters.IsActive);
        Assert.Equal("Basic", result.Filters.SearchTerm);
        Assert.Equal(PlanType.Basic, result.Filters.PlanType);
        Assert.True(result.Filters.HasTrialPeriod);
        Assert.Equal(5, result.Filters.ActiveFiltersCount);
    }

    [Fact]
    public async Task Handle_InvalidPageNumber_ThrowsException()
    {
        // Arrange
        var query = new GetAllPlansQuery(0, 10);

        // Act & Assert
        await Assert.ThrowsAsync<SubscriptionDomainException>(() => _handler.Handle(query, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_InvalidPageSize_ThrowsException()
    {
        // Arrange
        var query = new GetAllPlansQuery(1, 101);

        // Act & Assert
        await Assert.ThrowsAsync<SubscriptionDomainException>(() => _handler.Handle(query, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_SearchTermTooLong_ThrowsException()
    {
        // Arrange
        var longSearchTerm = new string('a', 101);
        var query = new GetAllPlansQuery(1, 10, searchTerm: longSearchTerm);

        // Act & Assert
        await Assert.ThrowsAsync<SubscriptionDomainException>(() => _handler.Handle(query, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_NoFilters_ReturnsAllPlans()
    {
        // Arrange
        var query = new GetAllPlansQuery(1, 10);
        var plans = CreateTestPlans();
        var planDtos = CreateTestPlanDtos();

        _mockPlanRepository.Setup(x => x.GetFilteredPlansAsync(1, 10, null, null, null, null, null))
            .ReturnsAsync((plans, 2));

        _mockMapper.Setup(x => x.Map<List<PlanDto>>(It.IsAny<List<Plan>>()))
            .Returns(planDtos);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Plans.Count);
        Assert.Equal(0, result.Filters.ActiveFiltersCount);
    }

    [Theory]
    [InlineData(UserType.TransportCompany)]
    [InlineData(UserType.Carrier)]
    [InlineData(UserType.Broker)]
    public async Task Handle_UserTypeFilter_CallsRepositoryWithCorrectFilter(UserType userType)
    {
        // Arrange
        var query = new GetAllPlansQuery(1, 10, userType);
        var plans = CreateTestPlans();
        var planDtos = CreateTestPlanDtos();

        _mockPlanRepository.Setup(x => x.GetFilteredPlansAsync(1, 10, userType, null, null, null, null))
            .ReturnsAsync((plans, 1));

        _mockMapper.Setup(x => x.Map<List<PlanDto>>(It.IsAny<List<Plan>>()))
            .Returns(planDtos);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockPlanRepository.Verify(x => x.GetFilteredPlansAsync(1, 10, userType, null, null, null, null), Times.Once);
        Assert.Equal(userType, result.Filters.UserType);
    }

    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    public async Task Handle_IsActiveFilter_CallsRepositoryWithCorrectFilter(bool isActive)
    {
        // Arrange
        var query = new GetAllPlansQuery(1, 10, isActive: isActive);
        var plans = CreateTestPlans();
        var planDtos = CreateTestPlanDtos();

        _mockPlanRepository.Setup(x => x.GetFilteredPlansAsync(1, 10, null, isActive, null, null, null))
            .ReturnsAsync((plans, 1));

        _mockMapper.Setup(x => x.Map<List<PlanDto>>(It.IsAny<List<Plan>>()))
            .Returns(planDtos);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        _mockPlanRepository.Verify(x => x.GetFilteredPlansAsync(1, 10, null, isActive, null, null, null), Times.Once);
        Assert.Equal(isActive, result.Filters.IsActive);
    }

    private List<Plan> CreateTestPlans()
    {
        var billingCycle = new BillingCycle(BillingInterval.Monthly, 1);
        var price = new Money(99.99m, "USD");
        var limits = new PlanLimits(10, 5, 3, false, false, false, false, false, false);

        return new List<Plan>
        {
            new Plan("Basic Plan", "Basic plan description", "1.0", PlanType.Basic, UserType.TransportCompany,
                price, billingCycle, limits, true, 30),
            new Plan("Pro Plan", "Pro plan description", "1.0", PlanType.Pro, UserType.Carrier,
                price, billingCycle, limits, true, 14)
        };
    }

    private List<PlanDto> CreateTestPlanDtos()
    {
        return new List<PlanDto>
        {
            new PlanDto
            {
                Id = Guid.NewGuid(),
                Name = "Basic Plan",
                Description = "Basic plan description",
                Type = PlanType.Basic,
                UserType = UserType.TransportCompany,
                IsActive = true
            },
            new PlanDto
            {
                Id = Guid.NewGuid(),
                Name = "Pro Plan",
                Description = "Pro plan description",
                Type = PlanType.Pro,
                UserType = UserType.Carrier,
                IsActive = true
            }
        };
    }
}
