### 2.1 Get All Subscription Plans (Including Inactive)
**Purpose**: Retrieve all plans for admin management, including inactive ones

```http
GET /api/admin/plans?includeInactive=true&userType=all
Authorization: Bearer {admin-token}
```

**Response**:
```json
{
  "plans": [
    {
      "id": "plan-basic-transport",
      "name": "Basic Transport Plan",
      "description": "Entry-level plan for transport companies",
      "version": "1.0",
      "type": "Basic",
      "userType": "TransportCompany",
      "price": 999.00,
      "currency": "INR",
      "billingInterval": "Monthly",
      "billingIntervalCount": 1,
      "customBillingDays": null,
      "isPublic": true,
      "trialPeriodDays": 14,
      "setupFee": null,
      "setupFeeCurrency": null,
      "limits": {
        "maxUsers": 3,
        "maxRFQs": 10,
        "storageGB": 5
      },
      "features": {
        "brokerNetworkAccess": "standard",
        "analyticsReports": "basic"
      },
      "isActive": true,
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-15T10:30:00Z"
    }
  ],
  "totalCount": 9
}
```

### 2.2 Create New Subscription Plan
**Purpose**: Create a new subscription plan

```http
POST /api/admin/plans
Authorization: Bearer {admin-token}
Content-Type: application/json

{
  "name": "Premium Transport Plan",
  "description": "Mid-tier plan with advanced features",
  "version": "1.0",
  "type": "Premium",
  "userType": "TransportCompany",
  "price": 1999.00,
  "currency": "INR",
  "billingInterval": "Monthly",
  "billingIntervalCount": 1,
  "isPublic": true,
  "trialPeriodDays": 30,
  "setupFee": 500.00,
  "setupFeeCurrency": "INR",
  "limits": {
    "maxUsers": 10,
    "maxRFQs": 50,
    "storageGB": 25
  },
  "features": {
    "brokerNetworkAccess": "premium",
    "analyticsReports": "advanced",
    "prioritySupport": true
  }
}
```

### 2.3 Update Subscription Plan
**Purpose**: Update existing plan details

```http
PUT /api/admin/plans/{planId}
Authorization: Bearer {admin-token}
Content-Type: application/json

{
  "name": "Premium Plus Transport Plan",
  "description": "Enhanced mid-tier plan",
  "price": 2199.00,
  "currency": "INR",
  "limits": {
    "maxUsers": 15,
    "maxRFQs": 75,
    "storageGB": 50
  }
}
```