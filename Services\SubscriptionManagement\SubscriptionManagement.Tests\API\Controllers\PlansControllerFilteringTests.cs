using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using System.Net;
using System.Text.Json;
using SubscriptionManagement.Application.Queries.GetAllPlans;
using SubscriptionManagement.Domain.Enums;
using Xunit;

namespace SubscriptionManagement.Tests.API.Controllers;

public class PlansControllerFilteringTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;

    public PlansControllerFilteringTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
    }

    [Fact]
    public async Task GetAllPlans_WithoutFilters_ReturnsOkWithPaginatedResults()
    {
        // Act
        var response = await _client.GetAsync("/api/Plans/admin/all?page=1&pageSize=10");

        // Assert
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<GetAllPlansResponse>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        Assert.NotNull(result);
        Assert.NotNull(result.Plans);
        Assert.Equal(1, result.Page);
        Assert.Equal(10, result.PageSize);
        Assert.True(result.TotalCount >= 0);
    }

    [Theory]
    [InlineData("TransportCompany")]
    [InlineData("Carrier")]
    [InlineData("Broker")]
    public async Task GetAllPlans_WithUserTypeFilter_ReturnsFilteredResults(string userType)
    {
        // Act
        var response = await _client.GetAsync($"/api/Plans/admin/all?userType={userType}");

        // Assert
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<GetAllPlansResponse>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        Assert.NotNull(result);
        Assert.NotNull(result.Filters);
        Assert.Equal(userType, result.Filters.UserType?.ToString());
        Assert.Equal(1, result.Filters.ActiveFiltersCount);
    }

    [Theory]
    [InlineData("true")]
    [InlineData("false")]
    public async Task GetAllPlans_WithIsActiveFilter_ReturnsFilteredResults(string isActive)
    {
        // Act
        var response = await _client.GetAsync($"/api/Plans/admin/all?isActive={isActive}");

        // Assert
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<GetAllPlansResponse>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        Assert.NotNull(result);
        Assert.NotNull(result.Filters);
        Assert.Equal(bool.Parse(isActive), result.Filters.IsActive);
        Assert.Equal(1, result.Filters.ActiveFiltersCount);
    }

    [Fact]
    public async Task GetAllPlans_WithSearchTerm_ReturnsFilteredResults()
    {
        // Act
        var response = await _client.GetAsync("/api/Plans/admin/all?searchTerm=basic");

        // Assert
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<GetAllPlansResponse>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        Assert.NotNull(result);
        Assert.NotNull(result.Filters);
        Assert.Equal("basic", result.Filters.SearchTerm);
        Assert.Equal(1, result.Filters.ActiveFiltersCount);
    }

    [Theory]
    [InlineData("Basic")]
    [InlineData("Standard")]
    [InlineData("Pro")]
    [InlineData("Premium")]
    [InlineData("Enterprise")]
    public async Task GetAllPlans_WithPlanTypeFilter_ReturnsFilteredResults(string planType)
    {
        // Act
        var response = await _client.GetAsync($"/api/Plans/admin/all?planType={planType}");

        // Assert
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<GetAllPlansResponse>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        Assert.NotNull(result);
        Assert.NotNull(result.Filters);
        Assert.Equal(planType, result.Filters.PlanType?.ToString());
        Assert.Equal(1, result.Filters.ActiveFiltersCount);
    }

    [Theory]
    [InlineData("true")]
    [InlineData("false")]
    public async Task GetAllPlans_WithTrialPeriodFilter_ReturnsFilteredResults(string hasTrialPeriod)
    {
        // Act
        var response = await _client.GetAsync($"/api/Plans/admin/all?hasTrialPeriod={hasTrialPeriod}");

        // Assert
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<GetAllPlansResponse>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        Assert.NotNull(result);
        Assert.NotNull(result.Filters);
        Assert.Equal(bool.Parse(hasTrialPeriod), result.Filters.HasTrialPeriod);
        Assert.Equal(1, result.Filters.ActiveFiltersCount);
    }

    [Fact]
    public async Task GetAllPlans_WithMultipleFilters_ReturnsFilteredResults()
    {
        // Act
        var response = await _client.GetAsync(
            "/api/Plans/admin/all?userType=TransportCompany&isActive=true&searchTerm=basic&planType=Basic&hasTrialPeriod=true");

        // Assert
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<GetAllPlansResponse>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        Assert.NotNull(result);
        Assert.NotNull(result.Filters);
        Assert.Equal(5, result.Filters.ActiveFiltersCount);
        Assert.Equal("TransportCompany", result.Filters.UserType?.ToString());
        Assert.True(result.Filters.IsActive);
        Assert.Equal("basic", result.Filters.SearchTerm);
        Assert.Equal("Basic", result.Filters.PlanType?.ToString());
        Assert.True(result.Filters.HasTrialPeriod);
    }

    [Fact]
    public async Task GetAllPlans_WithInvalidPageNumber_ReturnsBadRequest()
    {
        // Act
        var response = await _client.GetAsync("/api/Plans/admin/all?page=0");

        // Assert
        Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
    }

    [Fact]
    public async Task GetAllPlans_WithInvalidPageSize_ReturnsBadRequest()
    {
        // Act
        var response = await _client.GetAsync("/api/Plans/admin/all?pageSize=101");

        // Assert
        Assert.Equal(HttpStatusCode.BadRequest, response.StatusCode);
    }

    [Fact]
    public async Task GetAllPlans_WithPagination_ReturnsCorrectPaginationInfo()
    {
        // Act
        var response = await _client.GetAsync("/api/Plans/admin/all?page=1&pageSize=2");

        // Assert
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        
        var content = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<GetAllPlansResponse>(content, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        Assert.NotNull(result);
        Assert.Equal(1, result.Page);
        Assert.Equal(2, result.PageSize);
        Assert.True(result.TotalPages >= 1);
        Assert.True(result.Plans.Count <= 2);
    }
}
