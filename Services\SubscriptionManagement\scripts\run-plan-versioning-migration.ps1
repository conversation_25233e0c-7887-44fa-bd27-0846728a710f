# Plan Versioning Migration Script
# Description: Executes the plan versioning migration on the database
# Date: 2025-08-07

param(
    [string]$ConnectionString = "Host=**************;Port=5432;Database=TLI_SubscriptionManagement;Username=postgres;Password=************",
    [string]$MigrationFile = "migrations/plan-versioning-migration.sql"
)

Write-Host "=== Plan Versioning Migration Script ===" -ForegroundColor Green
Write-Host "Date: $(Get-Date)" -ForegroundColor Gray
Write-Host ""

# Check if PostgreSQL psql is available
try {
    $psqlVersion = psql --version
    Write-Host "PostgreSQL Client: $psqlVersion" -ForegroundColor Green
} catch {
    Write-Error "PostgreSQL psql client not found. Please install PostgreSQL client tools."
    exit 1
}

# Check if migration file exists
$migrationPath = Join-Path $PSScriptRoot "..\$MigrationFile"
if (-not (Test-Path $migrationPath)) {
    Write-Error "Migration file not found: $migrationPath"
    exit 1
}

Write-Host "Migration file: $migrationPath" -ForegroundColor Gray
Write-Host ""

# Parse connection string
$connParams = @{}
$ConnectionString.Split(';') | ForEach-Object {
    $parts = $_.Split('=', 2)
    if ($parts.Length -eq 2) {
        $connParams[$parts[0]] = $parts[1]
    }
}

$host = $connParams['Host']
$port = $connParams['Port']
$database = $connParams['Database']
$username = $connParams['Username']
$password = $connParams['Password']

Write-Host "Database Connection Details:" -ForegroundColor Yellow
Write-Host "  Host: $host" -ForegroundColor Gray
Write-Host "  Port: $port" -ForegroundColor Gray
Write-Host "  Database: $database" -ForegroundColor Gray
Write-Host "  Username: $username" -ForegroundColor Gray
Write-Host ""

# Confirm before proceeding
$confirmation = Read-Host "Do you want to proceed with the migration? (y/N)"
if ($confirmation -ne 'y' -and $confirmation -ne 'Y') {
    Write-Host "Migration cancelled by user." -ForegroundColor Yellow
    exit 0
}

Write-Host ""
Write-Host "Starting migration..." -ForegroundColor Green

# Set environment variable for password
$env:PGPASSWORD = $password

try {
    # Execute the migration
    Write-Host "Executing migration file..." -ForegroundColor Yellow
    
    $result = psql -h $host -p $port -U $username -d $database -f $migrationPath -v ON_ERROR_STOP=1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "=== MIGRATION COMPLETED SUCCESSFULLY ===" -ForegroundColor Green
        Write-Host "The plan versioning system is now ready to use!" -ForegroundColor Green
        Write-Host ""
        Write-Host "Next steps:" -ForegroundColor Yellow
        Write-Host "1. Update your application configuration" -ForegroundColor Gray
        Write-Host "2. Test the new API endpoints" -ForegroundColor Gray
        Write-Host "3. Verify plan versioning functionality" -ForegroundColor Gray
    } else {
        Write-Error "Migration failed with exit code: $LASTEXITCODE"
        exit $LASTEXITCODE
    }
} catch {
    Write-Error "Error executing migration: $($_.Exception.Message)"
    exit 1
} finally {
    # Clear password environment variable
    Remove-Item Env:PGPASSWORD -ErrorAction SilentlyContinue
}

Write-Host ""
Write-Host "Migration script completed." -ForegroundColor Green
