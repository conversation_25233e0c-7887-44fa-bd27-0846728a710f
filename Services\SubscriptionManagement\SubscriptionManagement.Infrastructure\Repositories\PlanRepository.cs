using Microsoft.EntityFrameworkCore;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.ValueObjects;
using SubscriptionManagement.Infrastructure.Persistence;

namespace SubscriptionManagement.Infrastructure.Repositories
{
    public class PlanRepository : IPlanRepository
    {
        private readonly SubscriptionDbContext _context;

        public PlanRepository(SubscriptionDbContext context)
        {
            _context = context;
        }

        public async Task<Plan?> GetByIdAsync(Guid id)
        {
            return await _context.Plans
                .Include(p => p.Features)
                .FirstOrDefaultAsync(p => p.Id == id);
        }

        public async Task<Plan?> GetByNameAsync(string name)
        {
            return await _context.Plans
                .Include(p => p.Features)
                .FirstOrDefaultAsync(p => p.Name == name);
        }

        public async Task<List<Plan>> GetAllAsync()
        {
            return await _context.Plans
                .Include(p => p.Features)
                .OrderBy(p => p.UserType)
                .ThenBy(p => p.Type)
                .ToListAsync();
        }

        public async Task<List<Plan>> GetActiveAsync()
        {
            return await _context.Plans
                .Include(p => p.Features)
                .Where(p => p.IsActive)
                .OrderBy(p => p.UserType)
                .ThenBy(p => p.Type)
                .ToListAsync();
        }

        public async Task<List<Plan>> GetPublicAsync()
        {
            return await _context.Plans
                .Include(p => p.Features)
                .Where(p => p.IsActive && p.IsPublic)
                .OrderBy(p => p.UserType)
                .ThenBy(p => p.Type)
                .ToListAsync();
        }

        public async Task<List<Plan>> GetByUserTypeAsync(UserType userType)
        {
            return await _context.Plans
                .Include(p => p.Features)
                .Where(p => p.UserType == userType && p.IsActive)
                .OrderBy(p => p.Type)
                .ToListAsync();
        }

        public async Task<List<Plan>> GetByTypeAsync(PlanType type)
        {
            return await _context.Plans
                .Include(p => p.Features)
                .Where(p => p.Type == type && p.IsActive)
                .OrderBy(p => p.UserType)
                .ToListAsync();
        }

        public async Task<List<Plan>> GetByUserTypeAndTypeAsync(UserType userType, PlanType type)
        {
            return await _context.Plans
                .Include(p => p.Features)
                .Where(p => p.UserType == userType && p.Type == type && p.IsActive)
                .ToListAsync();
        }

        public async Task<Plan?> GetByBusinessCombinationAsync(UserType userType, PlanType planType, BillingCycle billingCycle)
        {
            return await _context.Plans
                .Include(p => p.Features)
                .Where(p => p.UserType == userType &&
                           p.Type == planType &&
                           p.BillingCycle.Interval == billingCycle.Interval &&
                           p.BillingCycle.IntervalCount == billingCycle.IntervalCount &&
                           p.BillingCycle.CustomDays == billingCycle.CustomDays &&
                           p.IsActive)
                .FirstOrDefaultAsync();
        }

        public async Task<List<Plan>> GetPlansAsync(int page, int pageSize)
        {
            return await _context.Plans
                .Include(p => p.Features)
                .OrderBy(p => p.UserType)
                .ThenBy(p => p.Type)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        public async Task<int> GetPlansCountAsync()
        {
            return await _context.Plans.CountAsync();
        }

        public async Task AddAsync(Plan plan)
        {
            await _context.Plans.AddAsync(plan);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateAsync(Plan plan)
        {
            _context.Plans.Update(plan);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(Plan plan)
        {
            _context.Plans.Remove(plan);
            await _context.SaveChangesAsync();
        }

        public async Task<bool> ExistsAsync(Guid id)
        {
            return await _context.Plans.AnyAsync(p => p.Id == id);
        }

        public async Task<bool> ExistsByNameAsync(string name)
        {
            return await _context.Plans.AnyAsync(p => p.Name == name);
        }

        public async Task<(List<Plan> Plans, int TotalCount)> GetFilteredPlansAsync(
            int page,
            int pageSize,
            UserType? userType = null,
            bool? isActive = null,
            string? searchTerm = null,
            PlanType? planType = null)
        {
            var query = _context.Plans
                .Include(p => p.Features)
                .AsQueryable();

            // Apply filters
            if (userType.HasValue)
            {
                query = query.Where(p => p.UserType == userType.Value);
            }

            if (isActive.HasValue)
            {
                query = query.Where(p => p.IsActive == isActive.Value);
            }

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                var lowerSearchTerm = searchTerm.ToLower();
                query = query.Where(p => p.Name.ToLower().Contains(lowerSearchTerm));
            }

            if (planType.HasValue)
            {
                query = query.Where(p => p.Type == planType.Value);
            }

            // Get total count before pagination
            var totalCount = await query.CountAsync();

            // Apply pagination and ordering
            var plans = await query
                .OrderBy(p => p.UserType)
                .ThenBy(p => p.Type)
                .ThenBy(p => p.Name)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return (plans, totalCount);
        }

        // Usage tracking methods implementation
        public async Task<Plan?> GetByIdAndVersionAsync(Guid planId, int version)
        {
            return await _context.Plans
                .Include(p => p.Features)
                .FirstOrDefaultAsync(p => p.Id == planId && p.Version == version);
        }

        public async Task<bool> IsVersionInUseAsync(Guid planId, int version)
        {
            var activeStatuses = new[] {
                SubscriptionStatus.Active,
                SubscriptionStatus.Suspended,
                SubscriptionStatus.PendingCancellation,
                SubscriptionStatus.PendingUpgrade,
                SubscriptionStatus.PendingDowngrade
            };

            return await _context.Subscriptions
                .AnyAsync(s => s.PlanId == planId &&
                              s.PlanVersion == version &&
                              activeStatuses.Contains(s.Status));
        }

        public async Task<int> GetActiveSubscriberCountAsync(Guid planId, int version)
        {
            var activeStatuses = new[] {
                SubscriptionStatus.Active,
                SubscriptionStatus.Suspended,
                SubscriptionStatus.PendingCancellation,
                SubscriptionStatus.PendingUpgrade,
                SubscriptionStatus.PendingDowngrade
            };

            return await _context.Subscriptions
                .CountAsync(s => s.PlanId == planId &&
                               s.PlanVersion == version &&
                               activeStatuses.Contains(s.Status));
        }

        public async Task<Dictionary<UserType, int>> GetSubscriberCountByUserTypeAsync(Guid planId, int version)
        {
            var activeStatuses = new[] {
                SubscriptionStatus.Active,
                SubscriptionStatus.Suspended,
                SubscriptionStatus.PendingCancellation,
                SubscriptionStatus.PendingUpgrade,
                SubscriptionStatus.PendingDowngrade
            };

            var plan = await _context.Plans.FirstOrDefaultAsync(p => p.Id == planId && p.Version == version);
            if (plan == null) return new Dictionary<UserType, int>();

            var count = await _context.Subscriptions
                .CountAsync(s => s.PlanId == planId &&
                               s.PlanVersion == version &&
                               activeStatuses.Contains(s.Status));

            return new Dictionary<UserType, int> { { plan.UserType, count } };
        }



        public async Task<int> GetHighestVersionAsync(Guid planId)
        {
            var maxVersion = await _context.Plans
                .Where(p => p.PlanId == planId) // Use logical PlanId
                .MaxAsync(p => (int?)p.Version);

            return maxVersion ?? 1;
        }

        public async Task<List<Plan>> GetAllVersionsAsync(Guid planId)
        {
            return await _context.Plans
                .Include(p => p.Features)
                .Where(p => p.PlanId == planId) // Use logical PlanId
                .OrderBy(p => p.Version)
                .ToListAsync();
        }

        // New methods for PlanId-based versioning
        public async Task<Plan?> GetByPlanIdAndVersionAsync(Guid planId, int version)
        {
            return await _context.Plans
                .Include(p => p.Features)
                .FirstOrDefaultAsync(p => p.PlanId == planId && p.Version == version);
        }

        public async Task<bool> IsVersionInUseAsync(Guid planVersionId)
        {
            var activeStatuses = new[] {
                SubscriptionStatus.Active,
                SubscriptionStatus.Suspended,
                SubscriptionStatus.PendingCancellation,
                SubscriptionStatus.PendingUpgrade,
                SubscriptionStatus.PendingDowngrade
            };

            return await _context.Subscriptions
                .AnyAsync(s => s.PlanId == planVersionId && activeStatuses.Contains(s.Status));
        }

        public async Task<int> GetActiveSubscriberCountAsync(Guid planVersionId)
        {
            var activeStatuses = new[] {
                SubscriptionStatus.Active,
                SubscriptionStatus.Suspended,
                SubscriptionStatus.PendingCancellation,
                SubscriptionStatus.PendingUpgrade,
                SubscriptionStatus.PendingDowngrade
            };

            return await _context.Subscriptions
                .CountAsync(s => s.PlanId == planVersionId && activeStatuses.Contains(s.Status));
        }

        public async Task<Dictionary<UserType, int>> GetSubscriberCountByUserTypeAsync(Guid planVersionId)
        {
            var activeStatuses = new[] {
                SubscriptionStatus.Active,
                SubscriptionStatus.Suspended,
                SubscriptionStatus.PendingCancellation,
                SubscriptionStatus.PendingUpgrade,
                SubscriptionStatus.PendingDowngrade
            };

            var result = await _context.Subscriptions
                .Where(s => s.PlanId == planVersionId && activeStatuses.Contains(s.Status))
                .Join(_context.Plans, s => s.PlanId, p => p.Id, (s, p) => p.UserType)
                .GroupBy(userType => userType)
                .Select(g => new { UserType = g.Key, Count = g.Count() })
                .ToListAsync();

            return result.ToDictionary(x => x.UserType, x => x.Count);
        }
    }
}
