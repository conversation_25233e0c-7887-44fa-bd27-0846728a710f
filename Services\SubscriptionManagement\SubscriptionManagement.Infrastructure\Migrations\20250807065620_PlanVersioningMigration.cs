﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SubscriptionManagement.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class PlanVersioningMigration : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Step 1: Add PlanVersion column to Subscriptions
            migrationBuilder.AddColumn<int>(
                name: "PlanVersion",
                table: "Subscriptions",
                type: "integer",
                nullable: false,
                defaultValue: 1);

            // Step 2: Convert Plans.Version from VARCHAR to INTEGER with proper casting
            // Add temporary column
            migrationBuilder.AddColumn<int>(
                name: "VersionTemp",
                table: "Plans",
                type: "integer",
                nullable: true);

            // Convert existing string versions to integers using SQL
            migrationBuilder.Sql(@"
                UPDATE ""Plans""
                SET ""VersionTemp"" = CASE
                    WHEN ""Version"" ~ '^[0-9]+\.[0-9]+$' THEN CAST(SPLIT_PART(""Version"", '.', 1) AS INTEGER)
                    WHEN ""Version"" ~ '^[0-9]+$' THEN CAST(""Version"" AS INTEGER)
                    ELSE 1
                END;
            ");

            // Drop old Version column
            migrationBuilder.DropColumn(
                name: "Version",
                table: "Plans");

            // Rename temp column to Version
            migrationBuilder.RenameColumn(
                name: "VersionTemp",
                table: "Plans",
                newName: "Version");

            // Make Version column NOT NULL with default value
            migrationBuilder.AlterColumn<int>(
                name: "Version",
                table: "Plans",
                type: "integer",
                nullable: false,
                defaultValue: 1);

            // Step 3: Update existing subscriptions with their plan's current version
            migrationBuilder.Sql(@"
                UPDATE ""Subscriptions""
                SET ""PlanVersion"" = COALESCE(
                    (SELECT p.""Version""
                     FROM ""Plans"" p
                     WHERE p.""Id"" = ""Subscriptions"".""PlanId""),
                    1
                );
            ");

            // Step 4: Create indexes for better performance
            migrationBuilder.CreateIndex(
                name: "IX_Plans_Version",
                table: "Plans",
                column: "Version");

            migrationBuilder.CreateIndex(
                name: "IX_Plans_Name_Version",
                table: "Plans",
                columns: new[] { "Name", "Version" });

            migrationBuilder.CreateIndex(
                name: "IX_Subscriptions_PlanId_PlanVersion",
                table: "Subscriptions",
                columns: new[] { "PlanId", "PlanVersion" });

            migrationBuilder.CreateIndex(
                name: "IX_Subscriptions_PlanId_PlanVersion_Status",
                table: "Subscriptions",
                columns: new[] { "PlanId", "PlanVersion", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_Subscriptions_Status_PlanId_PlanVersion",
                table: "Subscriptions",
                columns: new[] { "Status", "PlanId", "PlanVersion" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Drop indexes
            migrationBuilder.DropIndex(
                name: "IX_Subscriptions_Status_PlanId_PlanVersion",
                table: "Subscriptions");

            migrationBuilder.DropIndex(
                name: "IX_Subscriptions_PlanId_PlanVersion_Status",
                table: "Subscriptions");

            migrationBuilder.DropIndex(
                name: "IX_Subscriptions_PlanId_PlanVersion",
                table: "Subscriptions");

            migrationBuilder.DropIndex(
                name: "IX_Plans_Name_Version",
                table: "Plans");

            migrationBuilder.DropIndex(
                name: "IX_Plans_Version",
                table: "Plans");

            // Drop PlanVersion column
            migrationBuilder.DropColumn(
                name: "PlanVersion",
                table: "Subscriptions");

            // Convert Version back to string
            migrationBuilder.AddColumn<string>(
                name: "VersionTemp",
                table: "Plans",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true);

            // Convert integer versions back to strings
            migrationBuilder.Sql(@"
                UPDATE ""Plans""
                SET ""VersionTemp"" = CAST(""Version"" AS VARCHAR);
            ");

            // Drop integer Version column
            migrationBuilder.DropColumn(
                name: "Version",
                table: "Plans");

            // Rename temp column back to Version
            migrationBuilder.RenameColumn(
                name: "VersionTemp",
                table: "Plans",
                newName: "Version");

            // Make Version column NOT NULL
            migrationBuilder.AlterColumn<string>(
                name: "Version",
                table: "Plans",
                type: "character varying(50)",
                maxLength: 50,
                nullable: false);
        }
    }
}
