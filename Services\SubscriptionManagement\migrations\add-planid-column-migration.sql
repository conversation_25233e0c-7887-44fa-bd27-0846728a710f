-- =====================================================
-- Plan Versioning Migration: Add PlanId Column
-- Adds logical plan grouping while keeping unique IDs per version
-- =====================================================

BEGIN TRANSACTION;

-- Step 1: Create backup table
SELECT * INTO Plans_Backup FROM Plans;

-- Step 2: Add PlanId column to Plans table
ALTER TABLE Plans ADD PlanId UNIQUEIDENTIFIER NULL;

-- Step 3: Create temporary mapping for logical plan grouping
-- Group existing plans by Name + UserType + Type to identify logical plans
CREATE TABLE #LogicalPlanMapping (
    LogicalPlanId UNIQUEIDENTIFIER,
    PlanName NVARCHAR(255),
    UserType INT,
    PlanType INT,
    CreatedAt DATETIME2
);

-- Step 4: Generate logical plan IDs for each unique plan combination
INSERT INTO #LogicalPlanMapping (LogicalPlanId, PlanName, UserType, PlanType, CreatedAt)
SELECT 
    NEWID() as LogicalPlanId,
    Name,
    UserType,
    Type,
    MIN(CreatedAt) as CreatedAt
FROM Plans
GROUP BY Name, UserType, Type;

-- Step 5: Update existing plans with their logical PlanId
UPDATE p SET 
    PlanId = lpm.LogicalPlanId
FROM Plans p
INNER JOIN #LogicalPlanMapping lpm ON 
    p.Name = lpm.PlanName AND 
    p.UserType = lpm.UserType AND 
    p.Type = lpm.PlanType;

-- Step 6: Make PlanId NOT NULL after populating data
ALTER TABLE Plans ALTER COLUMN PlanId UNIQUEIDENTIFIER NOT NULL;

-- Step 7: Recalculate version numbers within each logical plan group
WITH VersionedPlans AS (
    SELECT 
        Id,
        PlanId,
        ROW_NUMBER() OVER (PARTITION BY PlanId ORDER BY CreatedAt, Version) as NewVersion
    FROM Plans
)
UPDATE p SET 
    Version = vp.NewVersion
FROM Plans p
INNER JOIN VersionedPlans vp ON p.Id = vp.Id;

-- Step 8: Create indexes for performance
CREATE INDEX IX_Plans_PlanId ON Plans(PlanId);
CREATE INDEX IX_Plans_PlanId_Version ON Plans(PlanId, Version);
CREATE INDEX IX_Plans_UserType_Type ON Plans(UserType, Type);

-- Step 9: Add unique constraint to ensure one version per logical plan
ALTER TABLE Plans ADD CONSTRAINT UQ_Plans_PlanId_Version UNIQUE (PlanId, Version);

-- Step 10: Update PlanFeatures table if it exists
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'PlanFeatures')
BEGIN
    -- Drop existing foreign key
    IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_PlanFeatures_Plans_PlanId')
        ALTER TABLE PlanFeatures DROP CONSTRAINT FK_PlanFeatures_Plans_PlanId;
    
    -- Recreate foreign key to reference the unique Id (not the logical PlanId)
    ALTER TABLE PlanFeatures 
    ADD CONSTRAINT FK_PlanFeatures_Plans_PlanId 
    FOREIGN KEY (PlanId) REFERENCES Plans(Id);
END

-- Step 11: Verify data integrity
SELECT 
    'Migration Results' as Status,
    COUNT(*) as TotalPlans,
    COUNT(DISTINCT PlanId) as LogicalPlans,
    COUNT(DISTINCT Id) as UniqueVersions,
    MAX(Version) as MaxVersion
FROM Plans;

-- Step 12: Show logical plan groupings
SELECT 
    PlanId,
    Name,
    UserType,
    Type,
    COUNT(*) as VersionCount,
    MIN(Version) as MinVersion,
    MAX(Version) as MaxVersion
FROM Plans
GROUP BY PlanId, Name, UserType, Type
ORDER BY Name, UserType, Type;

-- Step 13: Clean up
DROP TABLE #LogicalPlanMapping;

COMMIT TRANSACTION;

PRINT 'PlanId column migration completed successfully!';
PRINT 'Each logical plan now has a PlanId that groups all its versions.';
PRINT 'Individual plan versions maintain unique Id values.';
