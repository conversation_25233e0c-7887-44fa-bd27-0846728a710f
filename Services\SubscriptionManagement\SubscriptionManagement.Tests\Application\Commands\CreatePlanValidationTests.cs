using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using SubscriptionManagement.Application.Commands.CreatePlan;
using SubscriptionManagement.Application.DTOs;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.ValueObjects;
using Shared.Messaging;
using Xunit;

namespace SubscriptionManagement.Tests.Application.Commands
{
    /// <summary>
    /// Tests for business combination validation in CreatePlanCommandHandler
    /// </summary>
    public class CreatePlanValidationTests
    {
        private readonly Mock<IPlanRepository> _mockPlanRepository;
        private readonly Mock<IMessageBroker> _mockMessageBroker;
        private readonly Mock<ILogger<CreatePlanCommandHandler>> _mockLogger;
        private readonly CreatePlanCommandHandler _handler;

        public CreatePlanValidationTests()
        {
            _mockPlanRepository = new Mock<IPlanRepository>();
            _mockMessageBroker = new Mock<IMessageBroker>();
            _mockLogger = new Mock<ILogger<CreatePlanCommandHandler>>();
            _handler = new CreatePlanCommandHandler(
                _mockPlanRepository.Object,
                _mockMessageBroker.Object,
                _mockLogger.Object);
        }

        [Fact]
        public async Task Handle_WithNoDuplicateBusinessCombination_ShouldCreatePlanSuccessfully()
        {
            // Arrange
            var command = CreateValidCommand();
            _mockPlanRepository.Setup(r => r.GetByBusinessCombinationAsync(
                It.IsAny<UserType>(), It.IsAny<PlanType>(), It.IsAny<BillingCycle>()))
                .ReturnsAsync((Plan?)null);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeEmpty();
            _mockPlanRepository.Verify(r => r.AddAsync(It.IsAny<Plan>()), Times.Once);
        }

        [Fact]
        public async Task Handle_WithDuplicateBusinessCombination_ShouldThrowException()
        {
            // Arrange
            var command = CreateValidCommand();
            var existingPlan = CreateExistingPlan();
            
            _mockPlanRepository.Setup(r => r.GetByBusinessCombinationAsync(
                command.UserType, command.Type, It.IsAny<BillingCycle>()))
                .ReturnsAsync(existingPlan);

            // Act & Assert
            var act = async () => await _handler.Handle(command, CancellationToken.None);
            await act.Should().ThrowAsync<SubscriptionDomainException>()
                .WithMessage("*plan with this configuration already exists*");
        }

        [Fact]
        public async Task Handle_SameNameDifferentUserType_ShouldAllowCreation()
        {
            // Test Case A: Same Name + Different UserType
            var command = CreateValidCommand();
            command.Name = "Basic Plan"; // Same name as existing
            command.UserType = UserType.Carrier; // Different user type
            command.Type = PlanType.Premium;
            command.BillingInterval = BillingInterval.Monthly;

            _mockPlanRepository.Setup(r => r.GetByBusinessCombinationAsync(
                UserType.Carrier, PlanType.Premium, It.IsAny<BillingCycle>()))
                .ReturnsAsync((Plan?)null); // No business combination conflict

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeEmpty();
            _mockPlanRepository.Verify(r => r.AddAsync(It.IsAny<Plan>()), Times.Once);
        }

        [Fact]
        public async Task Handle_SameNameDifferentPlanType_ShouldAllowCreation()
        {
            // Test Case B: Same Name + Different PlanType
            var command = CreateValidCommand();
            command.Name = "Basic Plan"; // Same name as existing
            command.UserType = UserType.TransportCompany; // Same user type
            command.Type = PlanType.Basic; // Different plan type
            command.BillingInterval = BillingInterval.Monthly;

            _mockPlanRepository.Setup(r => r.GetByBusinessCombinationAsync(
                UserType.TransportCompany, PlanType.Basic, It.IsAny<BillingCycle>()))
                .ReturnsAsync((Plan?)null); // No business combination conflict

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeEmpty();
            _mockPlanRepository.Verify(r => r.AddAsync(It.IsAny<Plan>()), Times.Once);
        }

        [Fact]
        public async Task Handle_DifferentNameSameBusinessCombination_ShouldThrowException()
        {
            // Test Case C: Different Name + Same Business Combination
            var command = CreateValidCommand();
            command.Name = "Premium Plan"; // Different name
            command.UserType = UserType.TransportCompany; // Same combination
            command.Type = PlanType.Premium;
            command.BillingInterval = BillingInterval.Monthly;

            var existingPlan = new Plan(
                "Basic Plan", // Different name
                "Basic Description",
                "1.0",
                PlanType.Premium, // Same business combination
                UserType.TransportCompany,
                Money.Create(99.99m, "INR"),
                BillingCycle.Monthly(),
                PlanLimits.ForTransportCompany(10));

            _mockPlanRepository.Setup(r => r.GetByBusinessCombinationAsync(
                UserType.TransportCompany, PlanType.Premium, It.IsAny<BillingCycle>()))
                .ReturnsAsync(existingPlan); // Business combination conflict

            // Act & Assert
            var act = async () => await _handler.Handle(command, CancellationToken.None);
            await act.Should().ThrowAsync<SubscriptionDomainException>()
                .WithMessage("*plan with this configuration already exists*");
        }

        [Fact]
        public async Task Handle_CompletelyNewCombination_ShouldAllowCreation()
        {
            // Test Case D: Completely New Combination
            var command = CreateValidCommand();
            command.Name = "Advanced Plan";
            command.UserType = UserType.Shipper; // Different from existing
            command.Type = PlanType.Enterprise; // Different from existing
            command.BillingInterval = BillingInterval.Weekly; // Different from existing

            _mockPlanRepository.Setup(r => r.GetByBusinessCombinationAsync(
                UserType.Shipper, PlanType.Enterprise, It.IsAny<BillingCycle>()))
                .ReturnsAsync((Plan?)null); // No business combination conflict

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeEmpty();
            _mockPlanRepository.Verify(r => r.AddAsync(It.IsAny<Plan>()), Times.Once);
        }

        [Fact]
        public async Task Handle_WithNoFeatures_ShouldThrowValidationException()
        {
            // Arrange
            var command = CreateValidCommand();
            command.Features = new List<PlanFeatureDto>(); // Empty features list

            // Act & Assert
            var validator = new CreatePlanCommandValidator();
            var validationResult = await validator.ValidateAsync(command);

            validationResult.IsValid.Should().BeFalse();
            validationResult.Errors.Should().Contain(e => e.ErrorMessage == "Please select at least one module.");
        }

        [Fact]
        public async Task Handle_WithValidFeatures_ShouldPassValidation()
        {
            // Arrange
            var command = CreateValidCommand();
            command.Features = new List<PlanFeatureDto>
            {
                new PlanFeatureDto
                {
                    FeatureId = Guid.NewGuid(),
                    Name = "RFQ Management",
                    Key = "rfq_management",
                    Description = "Manage RFQ requests",
                    IsEnabled = true
                }
            };

            // Act
            var validator = new CreatePlanCommandValidator();
            var validationResult = await validator.ValidateAsync(command);

            // Assert
            validationResult.IsValid.Should().BeTrue();
        }

        private CreatePlanCommand CreateValidCommand()
        {
            return new CreatePlanCommand
            {
                Name = "Test Plan",
                Description = "Test Description",
                Version = "1.0",
                Type = PlanType.Basic,
                UserType = UserType.TransportCompany,
                Price = 99.99m,
                Currency = "INR",
                BillingInterval = BillingInterval.Monthly,
                BillingIntervalCount = 1,
                IsPublic = true,
                Limits = new PlanLimitsDto
                {
                    RfqLimit = 10,
                    VehicleLimit = 5,
                    CarrierLimit = 3,
                    IsUnlimited = false
                },
                Features = new List<PlanFeatureDto>
                {
                    new PlanFeatureDto
                    {
                        FeatureId = Guid.NewGuid(),
                        Name = "Basic Feature",
                        Key = "basic_feature",
                        Description = "Basic plan feature",
                        IsEnabled = true
                    }
                }
            };
        }

        private Plan CreateExistingPlan()
        {
            return new Plan(
                "Existing Plan",
                "Existing Description",
                "1.0",
                PlanType.Basic,
                UserType.TransportCompany,
                Money.Create(99.99m, "INR"),
                BillingCycle.Monthly(),
                PlanLimits.ForTransportCompany(10));
        }
    }
}
