﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using UserManagement.Infrastructure.Data;

#nullable disable

namespace UserManagement.Infrastructure.Data.Migrations
{
    [DbContext(typeof(UserManagementDbContext))]
    partial class UserManagementDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.11")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("UserManagement.Domain.Entities.AuditLog", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Action")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("AdditionalData")
                        .IsRequired()
                        .HasColumnType("jsonb");

                    b.Property<string>("CorrelationId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<Guid?>("EntityId")
                        .HasColumnType("uuid");

                    b.Property<string>("EntityType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("IpAddress")
                        .IsRequired()
                        .HasMaxLength(45)
                        .HasColumnType("character varying(45)");

                    b.Property<bool>("IsImmutable")
                        .HasColumnType("boolean");

                    b.Property<string>("NewValues")
                        .HasMaxLength(4000)
                        .HasColumnType("character varying(4000)");

                    b.Property<string>("OldValues")
                        .HasMaxLength(4000)
                        .HasColumnType("character varying(4000)");

                    b.Property<string>("SessionId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Severity")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UserAgent")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uuid");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("UserRole")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("Action");

                    b.HasIndex("EntityId");

                    b.HasIndex("EntityType");

                    b.HasIndex("Severity");

                    b.HasIndex("Timestamp");

                    b.HasIndex("UserId");

                    b.ToTable("AuditLogs", (string)null);
                });

            modelBuilder.Entity("UserManagement.Domain.Entities.DocumentSubmission", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("OverallStatus")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ReviewNotes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("ReviewStartedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ReviewedBy")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("SubmittedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.Property<string>("UserType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("OverallStatus");

                    b.HasIndex("UserId")
                        .IsUnique();

                    b.HasIndex("UserType");

                    b.ToTable("DocumentSubmissions", (string)null);
                });

            modelBuilder.Entity("UserManagement.Domain.Entities.UserProfile", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AadharNumber")
                        .HasMaxLength(12)
                        .HasColumnType("character varying(12)");

                    b.Property<string>("AccountHolderName")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("AccountNumber")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("Address")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime?>("ApprovedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ApprovedBy")
                        .HasColumnType("uuid");

                    b.Property<int?>("ApprovedDocuments")
                        .HasColumnType("integer");

                    b.Property<bool?>("AutoRenewalEnabled")
                        .HasColumnType("boolean");

                    b.Property<string>("BankName")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("BrandName")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("BusinessPanNumber")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<string>("CinNumber")
                        .HasMaxLength(21)
                        .HasColumnType("character varying(21)");

                    b.Property<string>("City")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("CompanyContactEmail")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("CompanyContactPhone")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("CompanyLogo")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("CompanyName")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Country")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("DateOfBirthInAadhaar")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("DateOfBirthInPan")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("DateOfIncorporation")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("DeactivatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeactivationReason")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Email")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("FatherOrHusbandNameInAadhaar")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("FatherOrHusbandNameInPan")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("FirstName")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("GenderInAadhaar")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<string>("GstName")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("GstNumber")
                        .HasMaxLength(15)
                        .HasColumnType("character varying(15)");

                    b.Property<DateTime?>("GstRegistrationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("GstRegistrationNumber")
                        .HasMaxLength(15)
                        .HasColumnType("character varying(15)");

                    b.Property<string>("IFSCCode")
                        .HasMaxLength(11)
                        .HasColumnType("character varying(11)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeactivated")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("KycApprovedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("KycReminderCount")
                        .HasColumnType("integer");

                    b.Property<bool?>("KycRequiresResubmission")
                        .HasColumnType("boolean");

                    b.Property<string>("KycStatus")
                        .HasColumnType("text");

                    b.Property<DateTime?>("KycSubmittedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastDocumentUpload")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastKycReminderSent")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastLoginAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastLoginIp")
                        .HasColumnType("text");

                    b.Property<string>("LastName")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("LastPasswordChangeAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LegalName")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("LicenseNumber")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("NameAsPerAadhaar")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("NameAsPerPan")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int?>("OrderCount")
                        .HasColumnType("integer");

                    b.Property<string>("PanNumber")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<int?>("PendingDocuments")
                        .HasColumnType("integer");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<string>("PlaceOfBusiness")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("PostalCode")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<string>("PreferredLanguage")
                        .HasColumnType("text");

                    b.Property<string>("Region")
                        .HasColumnType("text");

                    b.Property<DateTime?>("RejectedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("RejectedDocuments")
                        .HasColumnType("integer");

                    b.Property<string>("RejectionReason")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("RequirePasswordChange")
                        .HasColumnType("boolean");

                    b.Property<int?>("RfqCount")
                        .HasColumnType("integer");

                    b.Property<string>("State")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("SubscriptionExpiryDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("SubscriptionFeatureLimits")
                        .HasColumnType("integer");

                    b.Property<decimal?>("SubscriptionMonthlyFee")
                        .HasColumnType("numeric");

                    b.Property<string>("SubscriptionPlan")
                        .HasColumnType("text");

                    b.Property<DateTime?>("SubscriptionStartDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal?>("TotalBusinessValue")
                        .HasColumnType("numeric");

                    b.Property<int?>("TotalDocuments")
                        .HasColumnType("integer");

                    b.Property<string>("TradeName")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int?>("TripCount")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.Property<string>("UserType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Email");

                    b.HasIndex("Status");

                    b.HasIndex("UserId")
                        .IsUnique();

                    b.HasIndex("UserType");

                    b.ToTable("UserProfiles", (string)null);
                });

            modelBuilder.Entity("UserManagement.Domain.Entities.DocumentSubmission", b =>
                {
                    b.OwnsMany("UserManagement.Domain.Entities.DocumentSubmission.Documents#UserManagement.Domain.Entities.Document", "Documents", b1 =>
                        {
                            b1.Property<Guid>("DocumentSubmissionId")
                                .HasColumnType("uuid");

                            b1.Property<Guid>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("uuid");

                            b1.Property<decimal>("ConfidenceScore")
                                .HasColumnType("numeric");

                            b1.Property<DateTime>("CreatedAt")
                                .HasColumnType("timestamp with time zone");

                            b1.Property<int>("DocumentType")
                                .HasColumnType("integer");

                            b1.Property<DateTime?>("ExpiryDate")
                                .HasColumnType("timestamp with time zone");

                            b1.Property<string>("ExtractedData")
                                .HasMaxLength(2000)
                                .HasColumnType("character varying(2000)");

                            b1.Property<string>("FileName")
                                .HasMaxLength(255)
                                .HasColumnType("character varying(255)");

                            b1.Property<string>("FilePath")
                                .HasMaxLength(500)
                                .HasColumnType("character varying(500)");

                            b1.Property<string>("FileSize")
                                .HasMaxLength(50)
                                .HasColumnType("character varying(50)");

                            b1.Property<string>("MimeType")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)");

                            b1.Property<Guid?>("PreviousVersionId")
                                .HasColumnType("uuid");

                            b1.Property<string>("RejectionReason")
                                .HasMaxLength(1000)
                                .HasColumnType("character varying(1000)");

                            b1.Property<string>("ReviewNotes")
                                .HasMaxLength(1000)
                                .HasColumnType("character varying(1000)");

                            b1.Property<DateTime?>("ReviewedAt")
                                .HasColumnType("timestamp with time zone");

                            b1.Property<int>("Status")
                                .HasColumnType("integer");

                            b1.Property<DateTime?>("UpdatedAt")
                                .HasColumnType("timestamp with time zone");

                            b1.Property<DateTime?>("UploadedAt")
                                .HasColumnType("timestamp with time zone");

                            b1.Property<int>("VerificationMethod")
                                .HasColumnType("integer");

                            b1.Property<int>("Version")
                                .HasColumnType("integer");

                            b1.HasKey("DocumentSubmissionId", "Id");

                            b1.HasIndex("DocumentType");

                            b1.HasIndex("Status");

                            b1.ToTable("Documents", (string)null);

                            b1.WithOwner()
                                .HasForeignKey("DocumentSubmissionId");
                        });

                    b.Navigation("Documents");
                });
#pragma warning restore 612, 618
        }
    }
}
