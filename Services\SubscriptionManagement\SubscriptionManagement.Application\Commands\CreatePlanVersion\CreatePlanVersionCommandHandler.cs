using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using SubscriptionManagement.Application.DTOs;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Exceptions;

namespace SubscriptionManagement.Application.Commands.CreatePlanVersion
{
    /// <summary>
    /// Handler for CreatePlanVersionCommand
    /// Implements plan versioning logic: creates new version if current is in use, otherwise allows modification
    /// </summary>
    public class CreatePlanVersionCommandHandler : IRequestHandler<CreatePlanVersionCommand, PlanVersionResponseDto>
    {
        private readonly IPlanRepository _planRepository;
        private readonly IMapper _mapper;
        private readonly ILogger<CreatePlanVersionCommandHandler> _logger;

        public CreatePlanVersionCommandHandler(
            IPlanRepository planRepository,
            IMapper mapper,
            ILogger<CreatePlanVersionCommandHandler> logger)
        {
            _planRepository = planRepository;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<PlanVersionResponseDto> Handle(CreatePlanVersionCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("🚀 [DEBUG] Processing plan version request for PlanId: {PlanId}, Version: {Version}",
                    request.PlanId, request.CurrentVersion);

                // Validate input
                _logger.LogDebug("🔍 [DEBUG] Validating input parameters");
                if (request.PlanId == Guid.Empty)
                {
                    _logger.LogError("❌ [DEBUG] Plan ID is empty");
                    throw new SubscriptionDomainException("Plan ID cannot be empty");
                }

                if (request.CurrentVersion < 0)
                {
                    _logger.LogError("❌ [DEBUG] Current version is negative: {Version}", request.CurrentVersion);
                    throw new SubscriptionDomainException("Current version must be greater than or equal to 0");
                }

                _logger.LogDebug("✅ [DEBUG] Input validation passed");

                // Get the specific plan version using logical PlanId
                _logger.LogDebug("🔍 [DEBUG] Fetching plan with logical PlanId {PlanId} and version {Version}",
                    request.PlanId, request.CurrentVersion);

                var currentPlan = await _planRepository.GetByPlanIdAndVersionAsync(request.PlanId, request.CurrentVersion);
                if (currentPlan == null)
                {
                    _logger.LogWarning("❌ [DEBUG] Plan with logical PlanId {PlanId} and version {Version} not found",
                        request.PlanId, request.CurrentVersion);
                    throw new SubscriptionDomainException($"Plan with logical PlanId {request.PlanId} and version {request.CurrentVersion} not found");
                }

                _logger.LogDebug("✅ [DEBUG] Found plan: {PlanName} (Unique ID: {UniqueId}, Logical PlanId: {PlanId}, Version: {Version})",
                    currentPlan.Name, currentPlan.Id, currentPlan.PlanId, currentPlan.Version);

                // Check if this specific version is in use by active subscribers (using unique plan version Id)
                _logger.LogDebug("🔍 [DEBUG] Checking if plan version is in use by active subscribers");
                var isVersionInUse = await _planRepository.IsVersionInUseAsync(currentPlan.Id); // Use unique Id

                _logger.LogInformation("📊 [DEBUG] Plan {PlanName} (Logical PlanId: {PlanId}, Version: {Version}) is in use: {IsInUse}",
                    currentPlan.Name, currentPlan.PlanId, currentPlan.Version, isVersionInUse);

                if (isVersionInUse)
                {
                    _logger.LogDebug("🔄 [DEBUG] Plan is in use - creating new version");

                    // Create new version by cloning the current plan
                    _logger.LogDebug("🔍 [DEBUG] Calling CreateNewPlanVersion");
                    var newVersion = await CreateNewPlanVersion(currentPlan);
                    _logger.LogDebug("✅ [DEBUG] New version created: {NewVersion}", newVersion.Version);

                    _logger.LogDebug("🔍 [DEBUG] Mapping new plan with usage statistics");
                    var newPlanDto = await MapPlanWithUsageStatistics(newVersion);
                    _logger.LogDebug("✅ [DEBUG] Mapping completed successfully");

                    _logger.LogInformation("🎉 [DEBUG] Created new plan version {NewVersion} for plan {PlanName} (ID: {PlanId})",
                        newVersion.Version, newVersion.Name, newVersion.Id);

                    return new PlanVersionResponseDto
                    {
                        Plan = newPlanDto,
                        CanModifyDirectly = false,
                        Message = $"Plan version {request.CurrentVersion} is in use by active subscribers. Created new version {newVersion.Version} for modification."
                    };
                }
                else
                {
                    _logger.LogDebug("✅ [DEBUG] Plan is not in use - allowing direct modification");

                    // Allow direct modification of unused version
                    _logger.LogDebug("🔍 [DEBUG] Mapping current plan with usage statistics");
                    var currentPlanDto = await MapPlanWithUsageStatistics(currentPlan);
                    _logger.LogDebug("✅ [DEBUG] Mapping completed successfully");

                    _logger.LogInformation("📝 [DEBUG] Plan version {Version} for plan {PlanName} (ID: {PlanId}) is not in use and can be modified directly",
                        request.CurrentVersion, currentPlan.Name, request.PlanId);

                    return new PlanVersionResponseDto
                    {
                        Plan = currentPlanDto,
                        CanModifyDirectly = true,
                        Message = $"Plan version {request.CurrentVersion} is not in use and can be modified directly."
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "💥 [DEBUG] Error occurred in CreatePlanVersionCommandHandler: {ErrorMessage}", ex.Message);
                _logger.LogError("💥 [DEBUG] Stack trace: {StackTrace}", ex.StackTrace);
                throw;
            }
        }

        private async Task<Domain.Entities.Plan> CreateNewPlanVersion(Domain.Entities.Plan currentPlan)
        {
            try
            {
                _logger.LogDebug("🔍 [DEBUG] CreateNewPlanVersion - Getting highest version for logical plan {PlanId}", currentPlan.PlanId);

                // Get the highest version number for this logical plan
                var highestVersion = await _planRepository.GetHighestVersionAsync(currentPlan.PlanId);
                var newVersion = highestVersion + 1;

                _logger.LogDebug("📊 [DEBUG] CreateNewPlanVersion - Highest version: {HighestVersion}, New version: {NewVersion}",
                    highestVersion, newVersion);

                // Create new version using the plan's CreateNewVersion method
                _logger.LogDebug("🔄 [DEBUG] CreateNewPlanVersion - Creating new version using domain method");
                var newPlan = currentPlan.CreateNewVersion(newVersion);
                _logger.LogDebug("✅ [DEBUG] CreateNewPlanVersion - Domain object created successfully");

                // Save the new plan version
                _logger.LogDebug("💾 [DEBUG] CreateNewPlanVersion - Saving new plan to repository");
                await _planRepository.AddAsync(newPlan);
                _logger.LogDebug("✅ [DEBUG] CreateNewPlanVersion - Plan saved successfully");

                _logger.LogInformation("🎉 [DEBUG] Successfully created new plan version {NewVersion} from version {CurrentVersion} for plan {PlanName}",
                    newVersion, currentPlan.Version, currentPlan.Name);

                return newPlan;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "💥 [DEBUG] Error in CreateNewPlanVersion: {ErrorMessage}", ex.Message);
                throw;
            }
        }

        private async Task<PlanWithUsageDto> MapPlanWithUsageStatistics(Domain.Entities.Plan plan)
        {
            try
            {
                _logger.LogDebug("🔍 [DEBUG] MapPlanWithUsageStatistics - Starting for plan {PlanId}, version {Version}",
                    plan.Id, plan.Version);

                // Map basic plan data
                _logger.LogDebug("🔄 [DEBUG] MapPlanWithUsageStatistics - Mapping basic plan data with AutoMapper");
                var planDto = _mapper.Map<PlanWithUsageDto>(plan);
                _logger.LogDebug("✅ [DEBUG] MapPlanWithUsageStatistics - AutoMapper mapping completed");

                // Get usage statistics using unique plan version Id
                _logger.LogDebug("📊 [DEBUG] MapPlanWithUsageStatistics - Getting active subscriber count for plan version {PlanVersionId}", plan.Id);
                var totalActiveSubscribers = await _planRepository.GetActiveSubscriberCountAsync(plan.Id); // Use unique Id
                _logger.LogDebug("📊 [DEBUG] MapPlanWithUsageStatistics - Total active subscribers: {Count}", totalActiveSubscribers);

                var usageStats = new PlanUsageStatisticsDto
                {
                    TotalActiveSubscribers = totalActiveSubscribers
                };

                // Get subscribers by user type using unique plan version Id
                _logger.LogDebug("👥 [DEBUG] MapPlanWithUsageStatistics - Getting subscribers by user type for plan version {PlanVersionId}", plan.Id);
                var subscribersByUserType = await _planRepository.GetSubscriberCountByUserTypeAsync(plan.Id); // Use unique Id
                _logger.LogDebug("👥 [DEBUG] MapPlanWithUsageStatistics - Found {Count} user type groups", subscribersByUserType.Count);

                usageStats.SubscribersByUserType = subscribersByUserType.ToDictionary(
                    kvp => kvp.Key.ToString(),
                    kvp => kvp.Value);

                // Determine if plan can be modified (no active subscribers)
                usageStats.CanModify = usageStats.TotalActiveSubscribers == 0;
                _logger.LogDebug("🔒 [DEBUG] MapPlanWithUsageStatistics - Can modify: {CanModify}", usageStats.CanModify);

                planDto.UsageStatistics = usageStats;

                _logger.LogDebug("✅ [DEBUG] MapPlanWithUsageStatistics - Completed successfully");
                return planDto;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "💥 [DEBUG] Error in MapPlanWithUsageStatistics: {ErrorMessage}", ex.Message);
                throw;
            }
        }
    }
}
