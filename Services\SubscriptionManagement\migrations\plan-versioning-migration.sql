-- Plan Versioning Migration
-- Description: Complete migration for plan versioning system
-- Date: 2025-08-07
-- Author: Augment Agent

-- Connect to the database
\c TLI_SubscriptionManagement;

BEGIN;

-- Step 1: Check if Version column exists and is VARCHAR
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'subscription' 
        AND table_name = 'Plans' 
        AND column_name = 'Version' 
        AND data_type = 'character varying'
    ) THEN
        -- Convert VARCHAR Version to INTEGER
        RAISE NOTICE 'Converting Plans.Version from VARCHAR to INTEGER...';
        
        -- Add temporary column
        ALTER TABLE subscription."Plans" ADD COLUMN "VersionTemp" INTEGER;
        
        -- Convert existing string versions to integers
        UPDATE subscription."Plans" 
        SET "VersionTemp" = CASE 
            WHEN "Version" ~ '^[0-9]+\.[0-9]+$' THEN CAST(SPLIT_PART("Version", '.', 1) AS INTEGER)
            WHEN "Version" ~ '^[0-9]+$' THEN CAST("Version" AS INTEGER)
            ELSE 1  -- Default to version 1 for any unrecognized format
        END;
        
        -- Drop old column and rename temp column
        ALTER TABLE subscription."Plans" DROP COLUMN "Version";
        ALTER TABLE subscription."Plans" RENAME COLUMN "VersionTemp" TO "Version";
        
        -- Add constraints
        ALTER TABLE subscription."Plans" ALTER COLUMN "Version" SET NOT NULL;
        ALTER TABLE subscription."Plans" ALTER COLUMN "Version" SET DEFAULT 1;
        
        RAISE NOTICE 'Plans.Version converted to INTEGER successfully.';
    ELSE
        RAISE NOTICE 'Plans.Version is already INTEGER or does not exist.';
    END IF;
END $$;

-- Step 2: Add PlanVersion to Subscriptions table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'subscription' 
        AND table_name = 'Subscriptions' 
        AND column_name = 'PlanVersion'
    ) THEN
        RAISE NOTICE 'Adding PlanVersion column to Subscriptions table...';
        
        -- Add PlanVersion column
        ALTER TABLE subscription."Subscriptions" 
        ADD COLUMN "PlanVersion" INTEGER NOT NULL DEFAULT 1;
        
        -- Update existing subscriptions with their plan's current version
        UPDATE subscription."Subscriptions" s
        SET "PlanVersion" = COALESCE(
            (SELECT p."Version" 
             FROM subscription."Plans" p 
             WHERE p."Id" = s."PlanId"), 
            1
        );
        
        RAISE NOTICE 'PlanVersion column added and populated successfully.';
    ELSE
        RAISE NOTICE 'PlanVersion column already exists in Subscriptions table.';
    END IF;
END $$;

-- Step 3: Create/Update indexes
RAISE NOTICE 'Creating/updating indexes...';

-- Drop existing indexes if they exist
DROP INDEX IF EXISTS subscription."IX_Plans_Version";
DROP INDEX IF EXISTS subscription."IX_Plans_Name_Version";
DROP INDEX IF EXISTS subscription."IX_Subscriptions_PlanId_PlanVersion";
DROP INDEX IF EXISTS subscription."IX_Subscriptions_PlanId_PlanVersion_Status";
DROP INDEX IF EXISTS subscription."IX_Subscriptions_Status_PlanId_PlanVersion";

-- Create new indexes for Plans table
CREATE INDEX "IX_Plans_Version" ON subscription."Plans"("Version");
CREATE INDEX "IX_Plans_Name_Version" ON subscription."Plans"("Name", "Version");

-- Create new indexes for Subscriptions table
CREATE INDEX "IX_Subscriptions_PlanId_PlanVersion" 
ON subscription."Subscriptions"("PlanId", "PlanVersion");

CREATE INDEX "IX_Subscriptions_PlanId_PlanVersion_Status" 
ON subscription."Subscriptions"("PlanId", "PlanVersion", "Status");

CREATE INDEX "IX_Subscriptions_Status_PlanId_PlanVersion" 
ON subscription."Subscriptions"("Status", "PlanId", "PlanVersion");

-- Step 4: Verify data integrity
RAISE NOTICE 'Verifying data integrity...';

-- Check that all plans have valid versions
DO $$
DECLARE
    invalid_plans INTEGER;
BEGIN
    SELECT COUNT(*) INTO invalid_plans
    FROM subscription."Plans"
    WHERE "Version" IS NULL OR "Version" <= 0;
    
    IF invalid_plans > 0 THEN
        RAISE EXCEPTION 'Found % plans with invalid versions', invalid_plans;
    END IF;
    
    RAISE NOTICE 'All plans have valid versions.';
END $$;

-- Check that all subscriptions have valid plan versions
DO $$
DECLARE
    invalid_subscriptions INTEGER;
BEGIN
    SELECT COUNT(*) INTO invalid_subscriptions
    FROM subscription."Subscriptions"
    WHERE "PlanVersion" IS NULL OR "PlanVersion" <= 0;
    
    IF invalid_subscriptions > 0 THEN
        RAISE EXCEPTION 'Found % subscriptions with invalid plan versions', invalid_subscriptions;
    END IF;
    
    RAISE NOTICE 'All subscriptions have valid plan versions.';
END $$;

-- Step 5: Display migration summary
DO $$
DECLARE
    plan_count INTEGER;
    subscription_count INTEGER;
    max_version INTEGER;
BEGIN
    SELECT COUNT(*) INTO plan_count FROM subscription."Plans";
    SELECT COUNT(*) INTO subscription_count FROM subscription."Subscriptions";
    SELECT COALESCE(MAX("Version"), 0) INTO max_version FROM subscription."Plans";
    
    RAISE NOTICE '=== MIGRATION SUMMARY ===';
    RAISE NOTICE 'Total Plans: %', plan_count;
    RAISE NOTICE 'Total Subscriptions: %', subscription_count;
    RAISE NOTICE 'Highest Plan Version: %', max_version;
    RAISE NOTICE 'Migration completed successfully!';
END $$;

COMMIT;

-- Success message
\echo 'Plan versioning migration completed successfully!'
\echo 'You can now use the new plan versioning features.'
