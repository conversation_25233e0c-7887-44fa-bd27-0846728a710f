# .NET-based Plan Versioning Migration Script
# Description: Executes the plan versioning migration using .NET and Npgsql
# Date: 2025-08-07

param(
    [string]$ConnectionString = "Host=**************;Port=5432;Database=TLI_SubscriptionManagement;Username=postgres;Password=************"
)

Write-Host "=== .NET Plan Versioning Migration Script ===" -ForegroundColor Green
Write-Host "Date: $(Get-Date)" -ForegroundColor Gray
Write-Host ""

# Check if .NET is available
try {
    $dotnetVersion = dotnet --version
    Write-Host ".NET SDK Version: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Error ".NET SDK not found. Please install .NET SDK from https://dotnet.microsoft.com/download"
    exit 1
}

# Create temporary migration project
$tempDir = Join-Path $env:TEMP "PlanVersioningMigration"
$projectDir = Join-Path $tempDir "MigrationRunner"

Write-Host "Creating temporary migration project..." -ForegroundColor Yellow

# Clean up if exists
if (Test-Path $tempDir) {
    Remove-Item $tempDir -Recurse -Force
}

New-Item -ItemType Directory -Path $projectDir -Force | Out-Null

# Create project file
$projectContent = @"
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Npgsql" Version="8.0.1" />
  </ItemGroup>
</Project>
"@

$projectContent | Out-File -FilePath (Join-Path $projectDir "MigrationRunner.csproj") -Encoding UTF8

# Create migration C# code
$migrationCode = @"
using Npgsql;
using System;
using System.Threading.Tasks;

class Program
{
    static async Task Main(string[] args)
    {
        var connectionString = args.Length > 0 ? args[0] : 
            "Host=**************;Port=5432;Database=TLI_SubscriptionManagement;Username=postgres;Password=************";
        
        Console.WriteLine("=== Plan Versioning Migration ===");
        Console.WriteLine($"Date: {DateTime.Now}");
        Console.WriteLine();
        
        try
        {
            await using var connection = new NpgsqlConnection(connectionString);
            await connection.OpenAsync();
            
            Console.WriteLine("Connected to database successfully!");
            Console.WriteLine();
            
            // Execute migration steps
            await ExecuteMigration(connection);
            
            Console.WriteLine();
            Console.WriteLine("=== MIGRATION COMPLETED SUCCESSFULLY ===");
            Console.WriteLine("The plan versioning system is now ready to use!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Migration failed: {ex.Message}");
            Environment.Exit(1);
        }
    }
    
    static async Task ExecuteMigration(NpgsqlConnection connection)
    {
        // Step 1: Convert Plans.Version from VARCHAR to INTEGER
        Console.WriteLine("Step 1: Converting Plans.Version to INTEGER...");
        
        var checkVersionColumnSql = @"
            SELECT data_type FROM information_schema.columns 
            WHERE table_schema = 'subscription' 
            AND table_name = 'Plans' 
            AND column_name = 'Version'";
        
        await using var checkCmd = new NpgsqlCommand(checkVersionColumnSql, connection);
        var dataType = await checkCmd.ExecuteScalarAsync() as string;
        
        if (dataType == "character varying")
        {
            Console.WriteLine("  Converting VARCHAR Version to INTEGER...");
            
            var migrationSql = @"
                BEGIN;
                
                -- Add temporary column
                ALTER TABLE subscription.""Plans"" ADD COLUMN ""VersionTemp"" INTEGER;
                
                -- Convert existing versions
                UPDATE subscription.""Plans"" 
                SET ""VersionTemp"" = CASE 
                    WHEN ""Version"" ~ '^[0-9]+\.[0-9]+$' THEN CAST(SPLIT_PART(""Version"", '.', 1) AS INTEGER)
                    WHEN ""Version"" ~ '^[0-9]+$' THEN CAST(""Version"" AS INTEGER)
                    ELSE 1
                END;
                
                -- Drop old column and rename
                ALTER TABLE subscription.""Plans"" DROP COLUMN ""Version"";
                ALTER TABLE subscription.""Plans"" RENAME COLUMN ""VersionTemp"" TO ""Version"";
                
                -- Add constraints
                ALTER TABLE subscription.""Plans"" ALTER COLUMN ""Version"" SET NOT NULL;
                ALTER TABLE subscription.""Plans"" ALTER COLUMN ""Version"" SET DEFAULT 1;
                
                COMMIT;";
            
            await using var migrationCmd = new NpgsqlCommand(migrationSql, connection);
            await migrationCmd.ExecuteNonQueryAsync();
            Console.WriteLine("  ✓ Plans.Version converted to INTEGER");
        }
        else
        {
            Console.WriteLine("  ✓ Plans.Version is already INTEGER");
        }
        
        // Step 2: Add PlanVersion to Subscriptions
        Console.WriteLine("Step 2: Adding PlanVersion to Subscriptions...");
        
        var checkPlanVersionSql = @"
            SELECT COUNT(*) FROM information_schema.columns 
            WHERE table_schema = 'subscription' 
            AND table_name = 'Subscriptions' 
            AND column_name = 'PlanVersion'";
        
        await using var checkPlanVersionCmd = new NpgsqlCommand(checkPlanVersionSql, connection);
        var planVersionExists = Convert.ToInt32(await checkPlanVersionCmd.ExecuteScalarAsync()) > 0;
        
        if (!planVersionExists)
        {
            var addPlanVersionSql = @"
                BEGIN;
                
                -- Add PlanVersion column
                ALTER TABLE subscription.""Subscriptions"" 
                ADD COLUMN ""PlanVersion"" INTEGER NOT NULL DEFAULT 1;
                
                -- Update existing subscriptions
                UPDATE subscription.""Subscriptions"" s
                SET ""PlanVersion"" = COALESCE(
                    (SELECT p.""Version"" 
                     FROM subscription.""Plans"" p 
                     WHERE p.""Id"" = s.""PlanId""), 
                    1
                );
                
                COMMIT;";
            
            await using var addPlanVersionCmd = new NpgsqlCommand(addPlanVersionSql, connection);
            await addPlanVersionCmd.ExecuteNonQueryAsync();
            Console.WriteLine("  ✓ PlanVersion column added and populated");
        }
        else
        {
            Console.WriteLine("  ✓ PlanVersion column already exists");
        }
        
        // Step 3: Create indexes
        Console.WriteLine("Step 3: Creating indexes...");
        
        var indexSql = @"
            -- Drop existing indexes if they exist
            DROP INDEX IF EXISTS subscription.""IX_Plans_Version"";
            DROP INDEX IF EXISTS subscription.""IX_Plans_Name_Version"";
            DROP INDEX IF EXISTS subscription.""IX_Subscriptions_PlanId_PlanVersion"";
            DROP INDEX IF EXISTS subscription.""IX_Subscriptions_PlanId_PlanVersion_Status"";
            DROP INDEX IF EXISTS subscription.""IX_Subscriptions_Status_PlanId_PlanVersion"";
            
            -- Create new indexes
            CREATE INDEX ""IX_Plans_Version"" ON subscription.""Plans""(""Version"");
            CREATE INDEX ""IX_Plans_Name_Version"" ON subscription.""Plans""(""Name"", ""Version"");
            CREATE INDEX ""IX_Subscriptions_PlanId_PlanVersion"" ON subscription.""Subscriptions""(""PlanId"", ""PlanVersion"");
            CREATE INDEX ""IX_Subscriptions_PlanId_PlanVersion_Status"" ON subscription.""Subscriptions""(""PlanId"", ""PlanVersion"", ""Status"");
            CREATE INDEX ""IX_Subscriptions_Status_PlanId_PlanVersion"" ON subscription.""Subscriptions""(""Status"", ""PlanId"", ""PlanVersion"");";
        
        await using var indexCmd = new NpgsqlCommand(indexSql, connection);
        await indexCmd.ExecuteNonQueryAsync();
        Console.WriteLine("  ✓ Indexes created successfully");
        
        // Step 4: Verify data integrity
        Console.WriteLine("Step 4: Verifying data integrity...");
        
        var verifySql = @"
            SELECT 
                (SELECT COUNT(*) FROM subscription.""Plans"") as plan_count,
                (SELECT COUNT(*) FROM subscription.""Subscriptions"") as subscription_count,
                (SELECT COALESCE(MAX(""Version""), 0) FROM subscription.""Plans"") as max_version,
                (SELECT COUNT(*) FROM subscription.""Plans"" WHERE ""Version"" IS NULL OR ""Version"" <= 0) as invalid_plans,
                (SELECT COUNT(*) FROM subscription.""Subscriptions"" WHERE ""PlanVersion"" IS NULL OR ""PlanVersion"" <= 0) as invalid_subscriptions";
        
        await using var verifyCmd = new NpgsqlCommand(verifySql, connection);
        await using var reader = await verifyCmd.ExecuteReaderAsync();
        
        if (await reader.ReadAsync())
        {
            var planCount = Convert.ToInt32(reader["plan_count"]);
            var subscriptionCount = Convert.ToInt32(reader["subscription_count"]);
            var maxVersion = Convert.ToInt32(reader["max_version"]);
            var invalidPlans = Convert.ToInt32(reader["invalid_plans"]);
            var invalidSubscriptions = Convert.ToInt32(reader["invalid_subscriptions"]);
            
            Console.WriteLine($"  ✓ Total Plans: {planCount}");
            Console.WriteLine($"  ✓ Total Subscriptions: {subscriptionCount}");
            Console.WriteLine($"  ✓ Highest Plan Version: {maxVersion}");
            
            if (invalidPlans > 0 || invalidSubscriptions > 0)
            {
                throw new Exception($"Data integrity check failed: {invalidPlans} invalid plans, {invalidSubscriptions} invalid subscriptions");
            }
            
            Console.WriteLine("  ✓ Data integrity verified");
        }
    }
}
"@

$migrationCode | Out-File -FilePath (Join-Path $projectDir "Program.cs") -Encoding UTF8

# Build and run the migration
Write-Host "Building migration project..." -ForegroundColor Yellow
Set-Location $projectDir

try {
    dotnet build --configuration Release --verbosity quiet
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Build successful. Running migration..." -ForegroundColor Green
        Write-Host ""
        
        dotnet run --configuration Release -- $ConnectionString
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host ""
            Write-Host "Migration completed successfully!" -ForegroundColor Green
        } else {
            Write-Error "Migration failed with exit code: $LASTEXITCODE"
        }
    } else {
        Write-Error "Build failed with exit code: $LASTEXITCODE"
    }
} finally {
    # Clean up
    Set-Location $PSScriptRoot
    if (Test-Path $tempDir) {
        Remove-Item $tempDir -Recurse -Force -ErrorAction SilentlyContinue
    }
}

Write-Host ""
Write-Host "Migration script completed." -ForegroundColor Green
