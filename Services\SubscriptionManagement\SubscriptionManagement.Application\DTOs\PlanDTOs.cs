using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Application.DTOs
{
    public class PlanDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int Version { get; set; }
        public PlanType Type { get; set; }
        public UserType UserType { get; set; }
        public decimal Price { get; set; }
        public string Currency { get; set; } = string.Empty;
        public string BillingCycle { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public bool IsPublic { get; set; }
        public DateTime? TrialPeriodDays { get; set; }
        public decimal? SetupFee { get; set; }
        public string? SetupFeeCurrency { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string? CreatedBy { get; set; }
        public string? UpdatedBy { get; set; }
        public PlanLimitsDto Limits { get; set; } = new();
        public List<PlanFeatureDto> Features { get; set; } = new();
    }

    public class CreatePlanDto
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int Version { get; set; } = 1;
        public PlanType Type { get; set; }
        public UserType UserType { get; set; }
        public decimal Price { get; set; }
        public string Currency { get; set; } = string.Empty;
        public BillingInterval BillingInterval { get; set; }
        public int BillingIntervalCount { get; set; } = 1;
        public int? CustomBillingDays { get; set; }
        public bool IsPublic { get; set; } = true;
        public int? TrialPeriodDays { get; set; }
        public decimal? SetupFee { get; set; }
        public string? SetupFeeCurrency { get; set; }
        public CreatePlanLimitsDto Limits { get; set; } = new();
        public List<CreatePlanFeatureDto> Features { get; set; } = new();
    }

    public class UpdatePlanDto
    {
        public string? Name { get; set; }
        public string? Description { get; set; }
        public int? Version { get; set; }
        public decimal? Price { get; set; }
        public bool? IsActive { get; set; }
        public bool? IsPublic { get; set; }
        public UpdatePlanLimitsDto? Limits { get; set; }
    }

    public class PlanLimitsDto
    {
        public int? RfqLimit { get; set; }
        public int? VehicleLimit { get; set; }
        public int? CarrierLimit { get; set; }
        public bool IsUnlimited { get; set; }
    }

    public class CreatePlanLimitsDto
    {
        public int? RfqLimit { get; set; }
        public int? VehicleLimit { get; set; }
        public int? CarrierLimit { get; set; }
        public bool IsUnlimited { get; set; }
    }

    public class UpdatePlanLimitsDto
    {
        public int? RfqLimit { get; set; }
        public int? VehicleLimit { get; set; }
        public int? CarrierLimit { get; set; }
        public bool? IsUnlimited { get; set; }
    }

    public class PlanFeatureDto
    {
        public Guid Id { get; set; }
        public Guid FeatureId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Key { get; set; } = string.Empty;
        public string? Description { get; set; }
        public bool IsEnabled { get; set; }
        public string DisplayName { get; set; } = string.Empty;
        public string AccessDescription { get; set; } = string.Empty;
    }

    public class CreatePlanFeatureDto
    {
        public Guid FeatureId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Key { get; set; } = string.Empty;
        public string? Description { get; set; }
        public bool IsEnabled { get; set; } = true;
    }

    public class UpdatePlanFeatureDto
    {
        public string Name { get; set; } = string.Empty;
        public string Key { get; set; } = string.Empty;
        public string? Description { get; set; }
        public bool? IsEnabled { get; set; }
    }

    public class PlanSummaryDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public int Version { get; set; }
        public PlanType Type { get; set; }
        public UserType UserType { get; set; }
        public decimal Price { get; set; }
        public string Currency { get; set; } = string.Empty;
        public string BillingCycle { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public bool IsPopular { get; set; }
        public List<string> KeyFeatures { get; set; } = new();
    }

    public class PlanDetailDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int Version { get; set; }
        public PlanType Type { get; set; }
        public UserType UserType { get; set; }
        public decimal Price { get; set; }
        public string Currency { get; set; } = string.Empty;
        public BillingInterval BillingInterval { get; set; }
        public int BillingIntervalCount { get; set; }
        public int? CustomBillingDays { get; set; }
        public bool IsPublic { get; set; }
        public int? TrialPeriodDays { get; set; }
        public decimal? SetupFee { get; set; }
        public string? SetupFeeCurrency { get; set; }
        public PlanLimitsDto Limits { get; set; } = new();
        public List<PlanFeatureDetailDto> Features { get; set; } = new();
    }

    public class PlanFeatureDetailDto
    {
        public Guid Id { get; set; }
        public Guid FeatureId { get; set; }
        public string FeatureName { get; set; } = string.Empty;
        public string Key { get; set; } = string.Empty;
        public string? Description { get; set; }
        public bool IsEnable { get; set; }
    }

    // Plan Usage Tracking DTOs
    public class PlanUsageStatisticsDto
    {
        public int TotalActiveSubscribers { get; set; }
        public Dictionary<string, int> SubscribersByUserType { get; set; } = new();
        public bool CanModify { get; set; }
    }



    public class PlanWithUsageDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int Version { get; set; }
        public PlanType Type { get; set; }
        public UserType UserType { get; set; }
        public decimal Price { get; set; }
        public string Currency { get; set; } = string.Empty;
        public BillingInterval BillingInterval { get; set; }
        public int BillingIntervalCount { get; set; }
        public int? CustomBillingDays { get; set; }
        public bool IsPublic { get; set; }
        public int? TrialPeriodDays { get; set; }
        public decimal? SetupFee { get; set; }
        public string? SetupFeeCurrency { get; set; }
        public PlanLimitsDto Limits { get; set; } = new();
        public List<PlanFeatureDetailDto> Features { get; set; } = new();
        public PlanUsageStatisticsDto UsageStatistics { get; set; } = new();
    }

    public class PlanVersionResponseDto
    {
        public PlanWithUsageDto Plan { get; set; } = new();
        public bool CanModifyDirectly { get; set; }
        public string Message { get; set; } = string.Empty;
    }
}
