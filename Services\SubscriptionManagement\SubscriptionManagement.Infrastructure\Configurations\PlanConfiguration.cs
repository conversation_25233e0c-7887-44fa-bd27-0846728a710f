using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.ValueObjects;

namespace SubscriptionManagement.Infrastructure.Configurations
{
    public class PlanConfiguration : IEntityTypeConfiguration<Plan>
    {
        public void Configure(EntityTypeBuilder<Plan> builder)
        {
            builder.ToTable("Plans");

            // Configure primary key - each plan version has unique Id
            builder.HasKey(p => p.Id);

            // Configure PlanId for logical plan grouping
            builder.Property(p => p.PlanId)
                .IsRequired()
                .HasComment("Logical plan identifier that groups all versions of the same plan");

            // Create unique constraint for PlanId + Version combination
            builder.HasIndex(p => new { p.PlanId, p.Version })
                .IsUnique()
                .HasDatabaseName("UQ_Plans_PlanId_Version");

            builder.Property(p => p.Name)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(p => p.Description)
                .IsRequired()
                .HasMaxLength(500);

            builder.Property(p => p.Version)
                .IsRequired()
                .HasDefaultValue(1);

            builder.Property(p => p.Type)
                .IsRequired()
                .HasConversion<string>();

            builder.Property(p => p.UserType)
                .IsRequired()
                .HasConversion<string>();

            // Configure Money value object for Price
            builder.OwnsOne(p => p.Price, price =>
            {
                price.Property(m => m.Amount)
                    .HasColumnName("Price")
                    .HasPrecision(18, 2)
                    .IsRequired();

                price.Property(m => m.Currency)
                    .HasColumnName("Currency")
                    .HasMaxLength(3)
                    .IsRequired();
            });

            // Configure BillingCycle value object
            builder.OwnsOne(p => p.BillingCycle, cycle =>
            {
                cycle.Property(c => c.Interval)
                    .HasColumnName("BillingInterval")
                    .HasConversion<string>()
                    .IsRequired();

                cycle.Property(c => c.IntervalCount)
                    .HasColumnName("BillingIntervalCount")
                    .IsRequired();

                cycle.Property(c => c.CustomDays)
                    .HasColumnName("CustomBillingDays");
            });

            // Configure PlanLimits value object
            builder.OwnsOne(p => p.Limits, limits =>
            {
                limits.Property(l => l.RfqLimit)
                    .HasColumnName("RfqLimit");

                limits.Property(l => l.VehicleLimit)
                    .HasColumnName("VehicleLimit");

                limits.Property(l => l.CarrierLimit)
                    .HasColumnName("CarrierLimit");

                limits.Property(l => l.IsUnlimited)
                    .HasColumnName("IsUnlimited")
                    .IsRequired();
            });

            builder.Property(p => p.IsActive)
                .IsRequired();

            builder.Property(p => p.IsPublic)
                .IsRequired();

            builder.Property(p => p.TrialPeriodDays)
                .HasColumnType("INTEGER");

            builder.Property(p => p.SetupFeeAmount);

            builder.Property(p => p.SetupFeeCurrency)
                .HasMaxLength(3);

            builder.Property(p => p.CreatedAt)
                .IsRequired();

            builder.Property(p => p.UpdatedAt);

            // Configure relationships
            builder.HasMany(p => p.Features)
                .WithOne()
                .HasForeignKey("PlanId")
                .OnDelete(DeleteBehavior.Cascade);

            // TaxConfigurations property temporarily removed due to EF Core configuration conflicts

            // Indexes for performance
            builder.HasIndex(p => p.PlanId)
                .HasDatabaseName("IX_Plans_PlanId");

            builder.HasIndex(p => p.Name);

            builder.HasIndex(p => new { p.UserType, p.Type })
                .HasDatabaseName("IX_Plans_UserType_Type");

            builder.HasIndex(p => p.IsActive)
                .HasDatabaseName("IX_Plans_IsActive");

            builder.HasIndex(p => p.Version)
                .HasDatabaseName("IX_Plans_Version");
            builder.HasIndex(p => p.IsPublic);
        }
    }
}
