using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SubscriptionManagement.Application.Commands.CreateTierConfigurationSetup;
using SubscriptionManagement.Application.Commands.UpdateTierConfigurationSetup;
using SubscriptionManagement.Application.Commands.DeleteTierConfigurationSetup;
using SubscriptionManagement.Application.Queries.GetTierConfigurationSetup;
using SubscriptionManagement.Application.Queries.GetAllTierConfigurationSetups;
using SubscriptionManagement.Application.DTOs;
using System.Security.Claims;

namespace SubscriptionManagement.API.Controllers
{
    //[Authorize]
    [AllowAnonymous]
    public class TierConfigurationSetupController : BaseController
    {
        /// <summary>
        /// Create a new tier configuration setup
        /// </summary>
        [HttpPost]
        [ProducesResponseType(typeof(Guid), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> Create([FromBody] CreateTierConfigurationSetupDto request)
        {
            try
            {
                var command = new CreateTierConfigurationSetupCommand
                {
                    Type = request.Type,
                    IsActive = request.IsActive,
                    IsCustom = request.IsCustom,
                    CreatedBy = GetCurrentUserRole() ?? "System"
                };

                var result = await Mediator.Send(command);
                return CreatedAtAction(nameof(GetById), new { id = result }, result);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Update an existing tier configuration setup
        /// </summary>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> Update(Guid id, [FromBody] UpdateTierConfigurationSetupDto request)
        {
            try
            {
                var command = new UpdateTierConfigurationSetupCommand
                {
                    Id = id,
                    Type = request.Type,
                    IsActive = request.IsActive,
                    IsCustom = request.IsCustom,
                    UpdatedBy = GetCurrentUserRole() ?? "System"
                };

                var result = await Mediator.Send(command);
                if (!result)
                {
                    return NotFound(new { message = "Tier configuration setup not found" });
                }

                return Ok(new { message = "Tier configuration setup updated successfully" });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get all tier configuration setups with optional filtering and pagination
        /// </summary>
        [HttpGet]
        [ProducesResponseType(typeof(TierConfigurationSetupListDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> GetAll(
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 10,
            [FromQuery] bool? isActive = null,
            [FromQuery] string? type = null,
            [FromQuery] bool? isCustom = null)
        {
            try
            {
                var query = new GetAllTierConfigurationSetupsQuery
                {
                    PageNumber = pageNumber,
                    PageSize = pageSize,
                    IsActive = isActive,
                    Type = type,
                    IsCustom = isCustom
                };

                var result = await Mediator.Send(query);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Get a tier configuration setup by ID
        /// </summary>
        [HttpGet("{id}")]
        [ProducesResponseType(typeof(TierConfigurationSetupDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> GetById(Guid id)
        {
            try
            {
                var query = new GetTierConfigurationSetupQuery { Id = id };
                var result = await Mediator.Send(query);

                if (result == null)
                {
                    return NotFound(new { message = "Tier configuration setup not found" });
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }

        /// <summary>
        /// Delete a tier configuration setup
        /// </summary>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> Delete(Guid id)
        {
            try
            {
                var command = new DeleteTierConfigurationSetupCommand
                {
                    Id = id,
                    DeletedBy = GetCurrentUserRole() ?? "System"
                };

                var result = await Mediator.Send(command);
                if (!result)
                {
                    return NotFound(new { message = "Tier configuration setup not found" });
                }

                return Ok(new { message = "Tier configuration setup deleted successfully" });
            }
            catch (Exception ex)
            {
                return HandleException(ex);
            }
        }
    }
}
