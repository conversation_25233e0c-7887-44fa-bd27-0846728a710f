using MediatR;
using SubscriptionManagement.Application.DTOs;
using SubscriptionManagement.Domain.Enums;

namespace SubscriptionManagement.Application.Queries.GetAllPlans;

/// <summary>
/// Query to get all plans with comprehensive filtering options
/// </summary>
public class GetAllPlansQuery : IRequest<GetAllPlansResponse>
{
    /// <summary>
    /// Page number for pagination (1-based)
    /// </summary>
    public int Page { get; set; } = 1;

    /// <summary>
    /// Number of items per page
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Filter by user type (e.g., TransportCompany, Carrier, Broker, etc.)
    /// </summary>
    public UserType? UserType { get; set; }

    /// <summary>
    /// Filter by plan status (true for active, false for inactive, null for all)
    /// </summary>
    public bool? IsActive { get; set; }

    /// <summary>
    /// Search term for partial plan name matching (case-insensitive)
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Filter by plan type (Basic, Standard, Pro, Premium, Enterprise)
    /// </summary>
    public PlanType? PlanType { get; set; }

    public GetAllPlansQuery() { }

    public GetAllPlansQuery(
        int page = 1,
        int pageSize = 10,
        UserType? userType = null,
        bool? isActive = null,
        string? searchTerm = null,
        PlanType? planType = null)
    {
        Page = page;
        PageSize = pageSize;
        UserType = userType;
        IsActive = isActive;
        SearchTerm = searchTerm;
        PlanType = planType;
    }
}

/// <summary>
/// Response for GetAllPlansQuery with pagination information
/// </summary>
public class GetAllPlansResponse
{
    /// <summary>
    /// List of plans matching the filter criteria with usage statistics
    /// </summary>
    public List<PlanWithUsageDto> Plans { get; set; } = new();

    /// <summary>
    /// Total number of plans matching the filter criteria (before pagination)
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Current page number
    /// </summary>
    public int Page { get; set; }

    /// <summary>
    /// Number of items per page
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Total number of pages
    /// </summary>
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);

    /// <summary>
    /// Whether there is a next page
    /// </summary>
    public bool HasNextPage => Page < TotalPages;

    /// <summary>
    /// Whether there is a previous page
    /// </summary>
    public bool HasPreviousPage => Page > 1;

    /// <summary>
    /// Applied filters summary
    /// </summary>
    public FilterSummary Filters { get; set; } = new();
}

/// <summary>
/// Summary of applied filters
/// </summary>
public class FilterSummary
{
    public UserType? UserType { get; set; }
    public bool? IsActive { get; set; }
    public string? SearchTerm { get; set; }
    public PlanType? PlanType { get; set; }
    public int ActiveFiltersCount =>
        (UserType.HasValue ? 1 : 0) +
        (IsActive.HasValue ? 1 : 0) +
        (!string.IsNullOrWhiteSpace(SearchTerm) ? 1 : 0) +
        (PlanType.HasValue ? 1 : 0);
}
