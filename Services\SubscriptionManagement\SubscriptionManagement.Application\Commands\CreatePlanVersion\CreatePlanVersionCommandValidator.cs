using FluentValidation;

namespace SubscriptionManagement.Application.Commands.CreatePlanVersion
{
    /// <summary>
    /// Validator for CreatePlanVersionCommand
    /// </summary>
    public class CreatePlanVersionCommandValidator : AbstractValidator<CreatePlanVersionCommand>
    {
        public CreatePlanVersionCommandValidator()
        {
            RuleFor(x => x.PlanId)
                .NotEmpty()
                .WithMessage("Plan ID is required");

            RuleFor(x => x.CurrentVersion)
                .GreaterThanOrEqualTo(0)
                .WithMessage("Current version must be greater than or equal to 0")
                .LessThanOrEqualTo(1000)
                .WithMessage("Current version cannot exceed 1000");
        }
    }
}
