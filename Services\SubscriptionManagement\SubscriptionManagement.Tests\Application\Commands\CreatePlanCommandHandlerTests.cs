using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using SubscriptionManagement.Application.Commands.CreatePlan;
using SubscriptionManagement.Application.DTOs;
using SubscriptionManagement.Application.Interfaces;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.ValueObjects;
using Shared.Messaging;
using Xunit;

namespace SubscriptionManagement.Tests.Application.Commands
{
    public class CreatePlanCommandHandlerTests
    {
        private readonly Mock<IPlanRepository> _mockPlanRepository;
        private readonly Mock<IMessageBroker> _mockMessageBroker;
        private readonly Mock<ILogger<CreatePlanCommandHandler>> _mockLogger;
        private readonly CreatePlanCommandHandler _handler;

        public CreatePlanCommandHandlerTests()
        {
            _mockPlanRepository = new Mock<IPlanRepository>();
            _mockMessageBroker = new Mock<IMessageBroker>();
            _mockLogger = new Mock<ILogger<CreatePlanCommandHandler>>();
            _handler = new CreatePlanCommandHandler(
                _mockPlanRepository.Object,
                _mockMessageBroker.Object,
                _mockLogger.Object);
        }

        [Fact]
        public async Task Handle_WithValidRequest_ShouldCreatePlanSuccessfully()
        {
            // Arrange
            var command = CreateValidCommand();
            _mockPlanRepository.Setup(r => r.GetByBusinessCombinationAsync(
                It.IsAny<UserType>(), It.IsAny<PlanType>(), It.IsAny<BillingCycle>()))
                .ReturnsAsync((Plan?)null);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeEmpty();
            _mockPlanRepository.Verify(r => r.AddAsync(It.IsAny<Plan>()), Times.Once);
            _mockMessageBroker.Verify(m => m.PublishAsync(It.IsAny<string>(), It.IsAny<object>()), Times.Once);
        }

        [Fact]
        public async Task Handle_WithDuplicateBusinessCombination_ShouldThrowException()
        {
            // Arrange
            var command = CreateValidCommand();
            var existingPlan = CreateExistingPlan();
            
            _mockPlanRepository.Setup(r => r.GetByBusinessCombinationAsync(
                command.UserType, command.Type, It.IsAny<BillingCycle>()))
                .ReturnsAsync(existingPlan);

            // Act & Assert
            var act = async () => await _handler.Handle(command, CancellationToken.None);
            await act.Should().ThrowAsync<SubscriptionDomainException>()
                .WithMessage("*business combination already exists*");
        }

        [Theory]
        [InlineData(UserType.TransportCompany, PlanType.Premium, BillingInterval.Monthly, UserType.Carrier, PlanType.Premium, BillingInterval.Monthly)] // Different UserType
        [InlineData(UserType.TransportCompany, PlanType.Premium, BillingInterval.Monthly, UserType.TransportCompany, PlanType.Basic, BillingInterval.Monthly)] // Different PlanType
        [InlineData(UserType.TransportCompany, PlanType.Premium, BillingInterval.Monthly, UserType.TransportCompany, PlanType.Premium, BillingInterval.Weekly)] // Different BillingCycle
        public async Task Handle_WithDifferentBusinessCombination_ShouldAllowCreation(
            UserType existingUserType, PlanType existingPlanType, BillingInterval existingBilling,
            UserType newUserType, PlanType newPlanType, BillingInterval newBilling)
        {
            // Arrange
            var command = CreateValidCommand();
            command.UserType = newUserType;
            command.Type = newPlanType;
            command.BillingInterval = newBilling;

            // Create existing plan with different combination
            var existingPlan = new Plan(
                "Existing Plan",
                "Existing Description",
                "1.0",
                existingPlanType,
                existingUserType,
                Money.Create(99.99m, "INR"),
                new BillingCycle(existingBilling),
                PlanLimits.ForTransportCompany(10));

            _mockPlanRepository.Setup(r => r.GetByBusinessCombinationAsync(
                newUserType, newPlanType, It.IsAny<BillingCycle>()))
                .ReturnsAsync((Plan?)null); // No conflict for new combination

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeEmpty();
            _mockPlanRepository.Verify(r => r.AddAsync(It.IsAny<Plan>()), Times.Once);
        }

        [Fact]
        public async Task Handle_WithSameNameButDifferentCombination_ShouldAllowCreation()
        {
            // Arrange - Test Case A: Same Name + Different UserType
            var command = CreateValidCommand();
            command.Name = "Basic"; // Same name
            command.UserType = UserType.Carrier; // Different user type
            command.Type = PlanType.Premium;
            command.BillingInterval = BillingInterval.Monthly;

            _mockPlanRepository.Setup(r => r.GetByBusinessCombinationAsync(
                UserType.Carrier, PlanType.Premium, It.IsAny<BillingCycle>()))
                .ReturnsAsync((Plan?)null); // No business combination conflict

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            result.Should().NotBeEmpty();
            _mockPlanRepository.Verify(r => r.AddAsync(It.IsAny<Plan>()), Times.Once);
        }

        [Fact]
        public async Task Handle_WithDifferentNameButSameCombination_ShouldThrowException()
        {
            // Arrange - Test Case C: Different Name + Same Combination
            var command = CreateValidCommand();
            command.Name = "Premium Plan"; // Different name
            command.UserType = UserType.TransportCompany; // Same combination as existing
            command.Type = PlanType.Premium;
            command.BillingInterval = BillingInterval.Monthly;

            var existingPlan = new Plan(
                "Basic Plan", // Different name
                "Basic Description",
                "1.0",
                PlanType.Premium, // Same combination
                UserType.TransportCompany,
                Money.Create(99.99m, "INR"),
                BillingCycle.Monthly(),
                PlanLimits.ForTransportCompany(10));

            _mockPlanRepository.Setup(r => r.GetByBusinessCombinationAsync(
                UserType.TransportCompany, PlanType.Premium, It.IsAny<BillingCycle>()))
                .ReturnsAsync(existingPlan); // Business combination conflict

            // Act & Assert
            var act = async () => await _handler.Handle(command, CancellationToken.None);
            await act.Should().ThrowAsync<SubscriptionDomainException>()
                .WithMessage("*business combination already exists*");
        }

        private CreatePlanCommand CreateValidCommand()
        {
            return new CreatePlanCommand
            {
                Name = "Test Plan",
                Description = "Test Description",
                Version = "1.0",
                Type = PlanType.Basic,
                UserType = UserType.TransportCompany,
                Price = 99.99m,
                Currency = "INR",
                BillingInterval = BillingInterval.Monthly,
                BillingIntervalCount = 1,
                IsPublic = true,
                Limits = new PlanLimitsDto
                {
                    RfqLimit = 10,
                    VehicleLimit = 5,
                    CarrierLimit = 3,
                    IsUnlimited = false
                },
                Features = new List<PlanFeatureDto>()
            };
        }

        private Plan CreateExistingPlan()
        {
            return new Plan(
                "Existing Plan",
                "Existing Description",
                "1.0",
                PlanType.Basic,
                UserType.TransportCompany,
                Money.Create(99.99m, "INR"),
                BillingCycle.Monthly(),
                PlanLimits.ForTransportCompany(10));
        }
    }
}
