# PowerShell script to create Entity Framework migration for PlanId column

# Navigate to the API project directory
Set-Location "TLIMicroservices\Services\SubscriptionManagement\SubscriptionManagement.API"

# Create the migration
dotnet ef migrations add AddPlanIdColumn --project ..\SubscriptionManagement.Infrastructure --startup-project . --context SubscriptionDbContext

Write-Host "Migration created successfully!"
Write-Host "Review the migration file before applying it to the database."
Write-Host ""
Write-Host "To apply the migration, run:"
Write-Host "dotnet ef database update --project ..\SubscriptionManagement.Infrastructure --startup-project . --context SubscriptionDbContext"
