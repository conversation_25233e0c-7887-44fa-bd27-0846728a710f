# Admin Subscription Management API Flow Documentation with Business Context

## Overview

This document provides comprehensive API flow documentation for ADMIN users managing subscriptions in the TLI Microservices platform. Each endpoint includes business context, use cases, and workflow explanations based on the actual codebase implementation.


## Authentication Headers

All admin requests must include:
```http
Authorization: Bearer {admin-jwt-token}
Content-Type: application/json
```

## 1. Admin Dashboard Overview

### 1.1 Get Subscription Statistics
**Business Purpose**: Provides real-time metrics for admin dashboard to monitor subscription health, revenue trends, and business performance.

**When to Use**: 
- Admin dashboard page load
- Periodic refresh for real-time monitoring
- Executive reporting and KPI tracking

**Why This Endpoint Exists**: Admins need quick visibility into subscription metrics without querying individual records. This aggregated view helps identify trends, issues, and opportunities.

```http
GET /api/admin/subscriptions/statistics
Authorization: Bearer {admin-token}
```

**Response**:
```json
{
  "totalSubscriptions": 1250,
  "activeSubscriptions": 980,
  "expiredSubscriptions": 150,
  "cancelledSubscriptions": 120,
  "subscriptionsByStatus": {
    "Active": 980,
    "Suspended": 50,
    "Cancelled": 120,
    "Expired": 150,
    "Pending": 30
  },
  "subscriptionsByPlan": {
    "Basic": 600,
    "Pro": 300,
    "Premium": 200,
    "Enterprise": 150
  },
  "monthlyRevenue": 2450000.00,
  "currency": "INR"
}
```

**Business Impact**: Enables data-driven decisions on pricing, plan popularity, and customer retention strategies.

## 2. Subscription Plan Management

### 2.1 Get All Subscription Plans
**Business Purpose**: Retrieve all available plans for administrative management, including inactive plans that customers can no longer subscribe to but existing subscribers may still use.

**When to Use**:
- Plan management interface loading
- Creating new subscriptions for customers
- Analyzing plan performance and usage
- Preparing plan updates or migrations

**Why This Endpoint Exists**: Admins need complete visibility into all plans (active and inactive) to manage existing subscriptions, plan migrations, and business strategy. The `includeInactive` parameter prevents accidental exposure of discontinued plans to customers while allowing admin management.

```http
GET /api/admin/plans?includeInactive=true&userType=TransportCompany
Authorization: Bearer {admin-token}
```

**Query Parameters Explained**:
- `includeInactive`: Shows discontinued plans still used by existing customers
- `userType`: Filters plans by target customer segment (TransportCompany, Broker, Carrier)

**Response**:
```json
{
  "plans": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "name": "Basic Transport Plan",
      "description": "Entry-level plan for transport companies",
      "version": "1.0",
      "type": "Basic",
      "userType": "TransportCompany",
      "price": 999.00,
      "currency": "INR",
      "billingInterval": "Monthly",
      "billingIntervalCount": 1,
      "customBillingDays": null,
      "isPublic": true,
      "trialPeriodDays": 14,
      "setupFeeAmount": null,
      "setupFeeCurrency": null,
      "limits": {
        "rfqLimit": 10,
        "vehicleLimit": null,
        "carrierLimit": null,
        "isUnlimited": false
      },
      "features": [],
      "isActive": true,
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-15T10:30:00Z"
    }
  ],
  "totalCount": 9
}
```

**Business Impact**: Enables strategic plan management, competitive analysis, and customer segmentation strategies.

### 2.2 Create New Subscription Plan
**Business Purpose**: Launch new subscription tiers to capture different market segments, respond to competitive pressure, or test new pricing strategies.

**When to Use**:
- Launching new product features requiring new pricing tiers
- Market expansion with region-specific plans
- A/B testing different pricing strategies
- Responding to competitive threats with new offerings

**Why This Endpoint Exists**: Business needs evolve, and new plans are essential for growth. This endpoint allows admins to quickly create and deploy new subscription options without developer intervention.

```http
POST /api/admin/plans
Authorization: Bearer {admin-token}
Content-Type: application/json

{
  "name": "Premium Transport Plan",
  "description": "Mid-tier plan with advanced features",
  "version": "1.0",
  "type": "Premium",
  "userType": "TransportCompany",
  "price": 1999.00,
  "currency": "INR",
  "billingInterval": "Monthly",
  "billingIntervalCount": 1,
  "customBillingDays": null,
  "isPublic": true,
  "trialPeriodDays": 30,
  "setupFee": 500.00,
  "setupFeeCurrency": "INR",
  "limits": {
    "rfqLimit": 50,
    "vehicleLimit": null,
    "carrierLimit": null,
    "isUnlimited": false
  },
  "features": [
    {
      "key": "brokerNetworkAccess",
      "value": "premium",
      "description": "Premium broker network access"
    },
    {
      "key": "analyticsReports",
      "value": "advanced",
      "description": "Advanced analytics and reporting"
    }
  ]
}
```

**Field Explanations**:
- `version`: Allows plan evolution while maintaining backward compatibility
- `isPublic`: Controls whether customers can self-subscribe or need admin assistance
- `trialPeriodDays`: Reduces customer acquisition friction
- `setupFee`: Covers onboarding costs for enterprise customers
- `limits`: Enforces usage boundaries to control costs and encourage upgrades
- `features`: Enables feature-based differentiation between plans

**Response**:
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440002",
  "message": "Plan created successfully"
}
```

**Business Impact**: Enables rapid market response, revenue optimization, and customer segmentation.

### 2.3 Update Subscription Plan
**Business Purpose**: Modify existing plans to adjust pricing, features, or limits based on market feedback, cost changes, or strategic pivots.

**When to Use**:
- Price adjustments due to cost changes or market conditions
- Feature updates based on customer feedback
- Limit adjustments to optimize resource usage
- Correcting plan configuration errors

**Why This Endpoint Exists**: Plans need continuous optimization. Rather than creating new plans for minor changes, updates maintain continuity while improving offerings.

```http
PUT /api/admin/plans/{planId}
Authorization: Bearer {admin-token}
Content-Type: application/json

{
  "name": "Premium Plus Transport Plan",
  "description": "Enhanced mid-tier plan with additional features",
  "price": 2199.00,
  "limits": {
    "rfqLimit": 75,
    "vehicleLimit": null,
    "carrierLimit": null,
    "isUnlimited": false
  }
}
```

**Business Impact**: Maintains competitive positioning and optimizes customer value without disrupting existing subscriptions.

### 2.4 Get Plan by ID
**Business Purpose**: Retrieve detailed plan information for customer support, billing inquiries, or detailed plan analysis.

**When to Use**:
- Customer support investigating billing issues
- Detailed plan analysis for business decisions
- Preparing plan comparison reports
- Troubleshooting subscription problems

**Why This Endpoint Exists**: Admins need detailed plan information to resolve customer issues and make informed business decisions.

```http
GET /api/admin/plans/{planId}
Authorization: Bearer {admin-token}
```

**Business Impact**: Enables effective customer support and informed business decision-making.

### 2.5 Activate/Deactivate Plan
**Business Purpose**: Control plan availability without deleting historical data, allowing for strategic plan lifecycle management.

**When to Use**:
- Discontinuing underperforming plans
- Temporarily removing plans during updates
- Seasonal plan availability (e.g., holiday promotions)
- Market testing with limited-time offers

**Why This Endpoint Exists**: Plans need lifecycle management. Deactivation preserves existing subscriptions while preventing new sign-ups, enabling graceful plan retirement.

```http
PATCH /api/admin/plans/{planId}/status
Authorization: Bearer {admin-token}
Content-Type: application/json

{
  "isActive": false,
  "reason": "Plan discontinued due to low adoption"
}
```

**Business Impact**: Enables strategic plan portfolio management and market positioning optimization.

## 3. User Subscription Management

### 3.1 Get All Subscriptions with Filtering
**Business Purpose**: Provide comprehensive subscription visibility with advanced filtering for customer support, billing management, and business analysis.

**When to Use**:
- Customer support searching for specific subscriptions
- Billing team investigating payment issues
- Business analysis of subscription patterns
- Compliance reporting and auditing

**Why This Endpoint Exists**: With thousands of subscriptions, admins need powerful search and filtering capabilities to quickly find relevant records and identify patterns.

```http
GET /api/admin/subscriptions?status=Active&userType=TransportCompany&page=1&pageSize=20&sortBy=createdAt&sortOrder=desc
Authorization: Bearer {admin-token}
```

**Query Parameters Explained**:
- `status`: Filter by subscription lifecycle stage for targeted management
- `userType`: Segment analysis by customer type
- `planType`: Analyze adoption patterns by plan tier
- `page`, `pageSize`: Handle large datasets efficiently
- `sortBy`, `sortOrder`: Prioritize most relevant results

**Response**:
```json
{
  "subscriptions": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "userId": "550e8400-e29b-41d4-a716-************",
      "planId": "550e8400-e29b-41d4-a716-************",
      "planName": "Basic Transport Plan",
      "status": "Active",
      "startDate": "2024-01-01T00:00:00Z",
      "endDate": "2024-02-01T00:00:00Z",
      "nextBillingDate": "2024-02-01T00:00:00Z",
      "currentPrice": 999.00,
      "currency": "INR",
      "autoRenew": true,
      "trialEndDate": "2024-01-15T00:00:00Z",
      "gracePeriodEnd": null,
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z"
    }
  ],
  "totalCount": 1250,
  "currentPage": 1,
  "totalPages": 125,
  "hasNext": true,
  "hasPrevious": false
}
```

**Business Impact**: Enables efficient customer support, proactive account management, and data-driven business insights.

### 3.2 Get Subscription Details
**Business Purpose**: Provide complete subscription context for customer support, billing disputes, and account management.

**When to Use**:
- Customer support resolving billing inquiries
- Account managers reviewing customer history
- Billing team investigating payment failures
- Compliance audits requiring detailed records

**Why This Endpoint Exists**: Customer issues often require complete subscription context including history, payments, and plan details. This comprehensive view enables effective problem resolution.

```http
GET /api/admin/subscriptions/{subscriptionId}
Authorization: Bearer {admin-token}
```

**Response**:
```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "userId": "550e8400-e29b-41d4-a716-************",
  "planId": "550e8400-e29b-41d4-a716-************",
  "plan": {
    "id": "550e8400-e29b-41d4-a716-************",
    "name": "Basic Transport Plan",
    "type": "Basic",
    "userType": "TransportCompany",
    "price": 999.00,
    "currency": "INR"
  },
  "status": "Active",
  "startDate": "2024-01-01T00:00:00Z",
  "endDate": "2024-02-01T00:00:00Z",
  "nextBillingDate": "2024-02-01T00:00:00Z",
  "currentPrice": 999.00,
  "currency": "INR",
  "autoRenew": true,
  "trialEndDate": "2024-01-15T00:00:00Z",
  "gracePeriodEnd": null,
  "prorationMode": "CreateProrations",
  "changeHistory": [
    {
      "changeType": "Created",
      "description": "Subscription created",
      "changedAt": "2024-01-01T00:00:00Z",
      "changedBy": "system"
    }
  ],
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}
```

**Key Fields Explained**:
- `changeHistory`: Audit trail for compliance and troubleshooting
- `prorationMode`: Controls billing adjustments during plan changes
- `gracePeriodEnd`: Manages payment failure scenarios
- `autoRenew`: Predicts revenue and identifies churn risks

**Business Impact**: Enables superior customer support and maintains detailed audit trails for compliance.

### 3.3 Create Subscription for User
**Business Purpose**: Enable admins to create subscriptions on behalf of customers for sales support, customer onboarding, or special arrangements.

**When to Use**:
- Sales team closing enterprise deals
- Customer support resolving billing issues
- Onboarding customers who need assistance
- Creating promotional or trial subscriptions

**Why This Endpoint Exists**: Not all customers can or should self-subscribe. Enterprise sales, customer support scenarios, and special arrangements require admin-assisted subscription creation.

```http
POST /api/admin/subscriptions
Authorization: Bearer {admin-token}
Content-Type: application/json

{
  "userId": "550e8400-e29b-41d4-a716-************",
  "planId": "550e8400-e29b-41d4-a716-************",
  "autoRenew": true,
  "startDate": "2024-01-15T00:00:00Z",
  "paymentMethodId": "pm_1234567890",
  "notes": "Created by admin for customer support case #12345"
}
```

**Field Explanations**:
- `startDate`: Allows future-dated subscriptions for contract alignment
- `paymentMethodId`: Links to pre-verified payment methods
- `notes`: Maintains context for audit and support purposes

**Response**:
```json
{
  "subscriptionId": "550e8400-e29b-41d4-a716-446655440030",
  "status": "Active",
  "message": "Subscription created successfully",
  "nextBillingDate": "2024-02-15T00:00:00Z"
}
```

**Business Impact**: Enables flexible sales processes and superior customer support experiences.

## 4. Subscription Modifications

### 4.1 Extend Subscription
**Business Purpose**: Provide grace periods for payment issues, customer goodwill gestures, or contract adjustments without complex billing changes.

**When to Use**:
- Payment processing delays or failures
- Customer goodwill for service issues
- Contract negotiations requiring time extensions
- Technical issues preventing normal billing

**Why This Endpoint Exists**: Rigid billing can damage customer relationships. Extensions provide flexibility to maintain service continuity while resolving issues.

```http
POST /api/admin/subscriptions/{subscriptionId}/extend
Authorization: Bearer {admin-token}
Content-Type: application/json

{
  "extensionDays": 15,
  "reason": "Customer payment processing delay",
  "applyAsGracePeriod": true,
  "notifyUser": true
}
```

**Field Explanations**:
- `extensionDays`: Specific duration prevents indefinite extensions
- `reason`: Audit trail for business decisions
- `applyAsGracePeriod`: Distinguishes from paid extensions
- `notifyUser`: Manages customer communication

**Response**:
```json
{
  "success": true,
  "message": "Subscription extended successfully",
  "newEndDate": "2024-02-16T00:00:00Z",
  "gracePeriodEnd": "2024-02-16T00:00:00Z"
}
```

**Business Impact**: Maintains customer relationships while providing operational flexibility for issue resolution.

### 4.2 Upgrade Subscription
**Business Purpose**: Enable immediate plan upgrades for customers who need additional features or capacity, capturing expansion revenue.

**When to Use**:
- Customer requests more features or capacity
- Sales team upselling existing customers
- Usage approaching plan limits
- Customer success initiatives

**Why This Endpoint Exists**: Customer needs grow over time. Immediate upgrades capture expansion revenue and prevent customer frustration from hitting limits.

```http
PUT /api/admin/subscriptions/{subscriptionId}/upgrade
Authorization: Bearer {admin-token}
Content-Type: application/json

{
  "newPlanId": "550e8400-e29b-41d4-a716-446655440003",
  "immediateUpgrade": true,
  "prorationMode": "CreateProrations",
  "reason": "Customer requested upgrade via support"
}
```

**Field Explanations**:
- `immediateUpgrade`: Provides instant access to new features
- `prorationMode`: Ensures fair billing for partial periods
- `reason`: Tracks upgrade drivers for business analysis

**Response**:
```json
{
  "success": true,
  "message": "Subscription upgraded successfully",
  "prorationAmount": 666.67,
  "currency": "INR",
  "nextBillingDate": "2024-02-01T00:00:00Z"
}
```

**Business Impact**: Maximizes customer lifetime value and prevents churn due to feature limitations.

### 4.3 Downgrade Subscription
**Business Purpose**: Retain customers who might otherwise cancel by offering lower-cost alternatives that still meet their reduced needs.

**When to Use**:
- Customer requests cost reduction
- Usage patterns indicate over-subscription
- Economic downturns affecting customer budgets
- Retention efforts for at-risk customers

**Why This Endpoint Exists**: Downgrades retain customers who might otherwise cancel entirely, preserving some revenue and maintaining the relationship for future growth.

```http
PUT /api/admin/subscriptions/{subscriptionId}/downgrade
Authorization: Bearer {admin-token}
Content-Type: application/json

{
  "newPlanId": "550e8400-e29b-41d4-a716-************",
  "effectiveDate": "end_of_billing_cycle",
  "reason": "Customer requested downgrade"
}
```

**Field Explanations**:
- `effectiveDate`: Balances customer needs with revenue protection
- `reason`: Identifies patterns for product and pricing optimization

**Business Impact**: Reduces churn and maintains customer relationships during economic challenges.

### 4.4 Cancel Subscription
**Business Purpose**: Process subscription cancellations while maintaining customer relationships and gathering feedback for business improvement.

**When to Use**:
- Customer requests cancellation
- Payment failures after grace period
- Policy violations requiring termination
- Business closure or customer exit

**Why This Endpoint Exists**: Cancellations are inevitable, but the process should be professional, compliant, and gather insights for business improvement.

```http
POST /api/admin/subscriptions/{subscriptionId}/cancel
Authorization: Bearer {admin-token}
Content-Type: application/json

{
  "reason": "Customer requested cancellation",
  "effectiveDate": "immediate",
  "refundAmount": 0,
  "notifyUser": true,
  "internalNotes": "Processed via support ticket #67890"
}
```

**Field Explanations**:
- `effectiveDate`: Balances customer service with revenue protection
- `refundAmount`: Handles prorated refunds fairly
- `internalNotes`: Maintains context for future interactions

**Business Impact**: Maintains professional relationships and gathers insights for churn reduction strategies.

### 4.5 Suspend Subscription
**Business Purpose**: Temporarily halt service for payment issues or policy violations while maintaining the possibility of reactivation.

**When to Use**:
- Payment failures requiring service interruption
- Policy violations needing investigation
- Customer-requested temporary suspension
- Technical issues requiring service pause

**Why This Endpoint Exists**: Suspension is less severe than cancellation, providing a path to resolution while protecting business interests.

```http
POST /api/admin/subscriptions/{subscriptionId}/suspend
Authorization: Bearer {admin-token}
Content-Type: application/json

{
  "reason": "Payment failure - multiple attempts",
  "suspendUntil": "2024-02-15T00:00:00Z",
  "notifyUser": true
}
```

**Field Explanations**:
- `suspendUntil`: Prevents indefinite suspensions
- `reason`: Documents business justification
- `notifyUser`: Manages customer communication

**Business Impact**: Protects business interests while maintaining recovery opportunities.

### 4.6 Reactivate Subscription
**Business Purpose**: Restore service after suspension resolution, maintaining customer relationships and recovering revenue.

**When to Use**:
- Payment issues resolved
- Policy violations addressed
- Customer-requested reactivation
- Technical issues resolved

**Why This Endpoint Exists**: Reactivation completes the suspension cycle, restoring customer value and revenue streams.

```http
POST /api/admin/subscriptions/{subscriptionId}/reactivate
Authorization: Bearer {admin-token}
Content-Type: application/json

{
  "reason": "Payment issue resolved",
  "resetUsageLimits": true
}
```

**Field Explanations**:
- `resetUsageLimits`: Provides fresh start for reactivated customers
- `reason`: Documents resolution for audit purposes

**Business Impact**: Recovers revenue and maintains customer satisfaction through effective issue resolution.

## 5. Payment and Billing Management

### 5.1 Get Payment History
**Business Purpose**: Provide complete payment visibility for billing disputes, financial reconciliation, and customer support.

**When to Use**:
- Customer disputes billing charges
- Financial reconciliation processes
- Audit requirements for payment records
- Customer support investigating payment issues

**Why This Endpoint Exists**: Payment transparency builds trust and enables effective dispute resolution. Complete payment history is essential for financial operations.

```http
GET /api/admin/subscriptions/{subscriptionId}/payments
Authorization: Bearer {admin-token}
```

**Response**:
```json
{
  "payments": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440040",
      "subscriptionId": "550e8400-e29b-41d4-a716-************",
      "amount": 999.00,
      "currency": "INR",
      "status": "Completed",
      "paymentMethod": "Card",
      "razorpayPaymentId": "pay_razorpay_123456",
      "createdAt": "2024-01-01T00:00:00Z",
      "processedAt": "2024-01-01T00:05:00Z"
    }
  ],
  "totalAmount": 2997.00,
  "totalPayments": 3
}
```

**Business Impact**: Enables transparent billing practices and effective financial operations.

### 5.2 Process Manual Payment
**Business Purpose**: Record payments received through alternative channels (bank transfers, checks, cash) to maintain accurate billing records.

**When to Use**:
- Bank transfer payments received
- Check payments processed
- Cash payments in regional offices
- Payment gateway failures requiring manual processing

**Why This Endpoint Exists**: Not all payments flow through automated systems. Manual payment recording ensures complete financial records and customer account accuracy.

```http
POST /api/admin/subscriptions/{subscriptionId}/manual-payment
Authorization: Bearer {admin-token}
Content-Type: application/json

{
  "amount": 999.00,
  "currency": "INR",
  "paymentMethod": "BankTransfer",
  "referenceNumber": "TXN123456789",
  "paymentDate": "2024-01-15T00:00:00Z",
  "notes": "Bank transfer verified via statement"
}
```

**Field Explanations**:
- `referenceNumber`: Links to external payment systems
- `paymentDate`: Accurate timing for financial reporting
- `notes`: Provides verification context

**Business Impact**: Maintains accurate financial records and supports diverse payment methods.

### 5.3 Issue Refund
**Business Purpose**: Process refunds for cancellations, service issues, or billing errors while maintaining customer satisfaction and compliance.

**When to Use**:
- Early cancellations requiring prorated refunds
- Service level agreement violations
- Billing errors requiring correction
- Customer goodwill gestures

**Why This Endpoint Exists**: Fair refund processes maintain customer trust and ensure regulatory compliance. Automated refund processing reduces manual effort and errors.

```http
POST /api/admin/subscriptions/{subscriptionId}/refund
Authorization: Bearer {admin-token}
Content-Type: application/json

{
  "amount": 500.00,
  "reason": "Partial refund for unused period",
  "refundMethod": "original_payment_method",
  "notes": "Prorated refund for early cancellation"
}
```

**Field Explanations**:
- `refundMethod`: Ensures compliance with payment regulations
- `reason`: Documents business justification
- `notes`: Provides additional context for audit

**Business Impact**: Maintains customer trust and ensures regulatory compliance while managing refund costs.

## 6. Tax Configuration Management

### 6.1 Get Global Tax Configurations
**Business Purpose**: Manage tax compliance across different jurisdictions and ensure accurate tax calculation for all subscriptions.

**When to Use**:
- Setting up operations in new regions
- Tax rate changes requiring updates
- Compliance audits requiring tax configuration review
- Troubleshooting tax calculation issues

**Why This Endpoint Exists**: Tax compliance is mandatory and complex. Centralized tax configuration ensures accurate calculations and regulatory compliance across all jurisdictions.

```http
GET /api/admin/tax/global-configurations
Authorization: Bearer {admin-token}
```

**Response**:
```json
{
  "configurations": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "taxType": "GST",
      "rate": 18.00,
      "isIncluded": false,
      "applicableRegions": ["IN"],
      "effectiveDate": "2024-01-01T00:00:00Z",
      "expiryDate": null,
      "priority": 1,
      "description": "Standard GST for digital services in India",
      "isActive": true,
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

**Field Explanations**:
- `isIncluded`: Determines if tax is added to or included in price
- `applicableRegions`: Ensures correct tax application by location
- `effectiveDate`/`expiryDate`: Manages tax rate changes over time
- `priority`: Handles overlapping tax rules

**Business Impact**: Ensures tax compliance and accurate billing across all markets.

### 6.2 Create Tax Configuration
**Business Purpose**: Add new tax rules for market expansion or regulatory changes.

**When to Use**:
- Expanding to new geographic markets
- New tax regulations requiring implementation
- Special tax rates for specific customer segments
- Compliance with changing tax laws

**Why This Endpoint Exists**: Tax regulations change frequently and vary by jurisdiction. Flexible tax configuration enables rapid compliance with new requirements.

```http
POST /api/admin/tax/global-configurations
Authorization: Bearer {admin-token}
Content-Type: application/json

{
  "taxType": "CGST",
  "rate": 9.00,
  "isIncluded": false,
  "applicableRegions": ["IN"],
  "effectiveDate": "2024-01-01T00:00:00Z",
  "priority": 1,
  "description": "Central GST component for India"
}
```

**Business Impact**: Enables rapid market expansion while maintaining tax compliance.

### 6.3 Create Tax Exemption
**Business Purpose**: Manage tax exemptions for qualified customers (educational institutions, non-profits, government entities).

**When to Use**:
- Educational institutions with tax exemptions
- Non-profit organizations qualifying for exemptions
- Government entities with special tax status
- Export customers requiring tax exemptions

**Why This Endpoint Exists**: Many jurisdictions provide tax exemptions for qualified entities. Proper exemption management ensures compliance while providing competitive advantages.

```http
POST /api/admin/tax/exemptions
Authorization: Bearer {admin-token}
Content-Type: application/json

{
  "customerId": "550e8400-e29b-41d4-a716-************",
  "exemptionType": "Educational",
  "exemptionNumber": "EDU123456789",
  "issuingAuthority": "Ministry of Education, India",
  "validFrom": "2024-01-01T00:00:00Z",
  "validTo": "2024-12-31T23:59:59Z",
  "exemptTaxTypes": ["GST", "CGST", "SGST"],
  "applicableRegions": ["IN"]
}
```

**Field Explanations**:
- `exemptionNumber`: Links to official exemption certificates
- `issuingAuthority`: Validates exemption legitimacy
- `validFrom`/`validTo`: Manages time-limited exemptions
- `exemptTaxTypes`: Specifies which taxes are exempted

**Business Impact**: Enables competitive pricing for qualified customers while maintaining tax compliance.

## 7. Feature Flag Management

### 7.1 Get Plan Features
**Business Purpose**: Review feature configurations for plans to ensure proper customer access and plan differentiation.

**When to Use**:
- Customer support investigating feature access issues
- Product management reviewing plan differentiation
- Sales team understanding plan capabilities
- Technical support troubleshooting feature problems

**Why This Endpoint Exists**: Feature-based plan differentiation is complex. Clear visibility into feature configurations enables effective support and sales processes.

```http
GET /api/admin/plans/{planId}/features
Authorization: Bearer {admin-token}
```

**Business Impact**: Enables effective customer support and maintains clear plan differentiation.

### 7.2 Update Plan Features
**Business Purpose**: Modify feature access for plans to respond to market needs, competitive pressure, or product evolution.

**When to Use**:
- Adding new features to existing plans
- Adjusting plan differentiation based on market feedback
- Responding to competitive feature offerings
- Correcting feature configuration errors

**Why This Endpoint Exists**: Product features evolve continuously. Dynamic feature management enables rapid market response without creating new plans.

```http
PUT /api/admin/plans/{planId}/features
Authorization: Bearer {admin-token}
Content-Type: application/json

{
  "features": [
    {
      "key": "advanced_analytics",
      "value": "true",
      "description": "Enable advanced analytics dashboard"
    },
    {
      "key": "api_access",
      "value": "false",
      "description": "API access for integrations"
    }
  ]
}
```

**Field Explanations**:
- `key`: Standardized feature identifiers for system integration
- `value`: Flexible values supporting boolean, numeric, and string configurations
- `description`: Human-readable feature explanations for support teams

**Business Impact**: Enables rapid product evolution and competitive response.

## 8. Usage and Limits Management

### 8.1 Get Subscription Usage
**Business Purpose**: Monitor customer usage patterns to identify upgrade opportunities, prevent overages, and optimize plan offerings.

**When to Use**:
- Customer approaching usage limits
- Account management identifying upsell opportunities
- Customer support investigating usage-related issues
- Product management analyzing usage patterns

**Why This Endpoint Exists**: Usage-based limits require monitoring to prevent service disruption and identify growth opportunities. Real-time usage visibility enables proactive customer management.

```http
GET /api/admin/subscriptions/{subscriptionId}/usage
Authorization: Bearer {admin-token}
```

**Response**:
```json
{
  "subscriptionId": "550e8400-e29b-41d4-a716-************",
  "currentPeriod": {
    "startDate": "2024-01-01T00:00:00Z",
    "endDate": "2024-02-01T00:00:00Z"
  },
  "usage": {
    "rfqsUsed": 7,
    "rfqsLimit": 10,
    "rfqsRemaining": 3,
    "vehiclesUsed": 2,
    "vehiclesLimit": null,
    "carriersUsed": 1,
    "carriersLimit": null
  },
  "isUnlimited": false,
  "lastUpdated": "2024-01-15T10:30:00Z"
}
```

**Field Explanations**:
- `currentPeriod`: Defines usage measurement timeframe
- `remaining`: Enables proactive limit management
- `isUnlimited`: Identifies premium customers without restrictions
- `lastUpdated`: Ensures data freshness for decision-making

**Business Impact**: Enables proactive customer management and identifies revenue expansion opportunities.

### 8.2 Reset Usage Limits
**Business Purpose**: Provide customer service flexibility for usage limit issues caused by system errors, billing problems, or special circumstances.

**When to Use**:
- System errors causing incorrect usage tracking
- Customer goodwill gestures for service issues
- Billing cycle adjustments requiring usage resets
- Technical issues requiring usage recalculation

**Why This Endpoint Exists**: Usage tracking systems can have errors, and customer situations require flexibility. Usage resets provide customer service tools for issue resolution.

```http
POST /api/admin/subscriptions/{subscriptionId}/reset-usage
Authorization: Bearer {admin-token}
Content-Type: application/json

{
  "reason": "Customer support request - billing system error",
  "resetTypes": ["rfqs", "vehicles", "carriers"]
}
```

**Field Explanations**:
- `reason`: Documents business justification for audit
- `resetTypes`: Selective reset prevents unintended consequences

**Business Impact**: Maintains customer satisfaction while providing operational flexibility for issue resolution.

## 9. Reports and Analytics

### 9.1 Generate Subscription Report
**Business Purpose**: Provide comprehensive business intelligence for strategic decision-making, financial planning, and performance analysis.

**When to Use**:
- Monthly/quarterly business reviews
- Financial planning and forecasting
- Investor reporting requirements
- Strategic planning initiatives

**Why This Endpoint Exists**: Data-driven decision making requires comprehensive reporting. Automated report generation provides consistent, accurate business intelligence.

```http
POST /api/admin/reports/subscriptions
Authorization: Bearer {admin-token}
Content-Type: application/json

{
  "reportType": "Revenue",
  "startDate": "2024-01-01T00:00:00Z",
  "endDate": "2024-01-31T23:59:59Z",
  "groupBy": "Plan",
  "includeDetails": true,
  "format": "JSON"
}
```

**Field Explanations**:
- `reportType`: Focuses analysis on specific business metrics
- `groupBy`: Enables segmented analysis for insights
- `includeDetails`: Balances overview with detailed analysis
- `format`: Supports different consumption patterns

**Response**:
```json
{
  "reportId": "550e8400-e29b-41d4-a716-446655440060",
  "status": "Completed",
  "data": {
    "totalRevenue": 2450000.00,
    "currency": "INR",
    "planBreakdown": [
      {
        "planName": "Basic Transport Plan",
        "subscriptionCount": 600,
        "revenue": 599400.00
      },
      {
        "planName": "Pro Transport Plan",
        "subscriptionCount": 300,
        "revenue": 899700.00
      }
    ]
  },
  "generatedAt": "2024-01-15T10:35:00Z"
}
```

**Business Impact**: Enables data-driven strategic decisions and accurate financial planning.

## 10. Common Admin Workflows with Business Context

### Workflow 1: New Customer Setup
**Business Scenario**: Sales team closes enterprise deal requiring immediate customer onboarding with custom configuration.

**Steps**:
1. `GET /api/admin/plans?userType=TransportCompany` - Review available plans for customer segment
2. `POST /api/admin/subscriptions` - Create subscription with enterprise terms
3. `GET /api/admin/subscriptions/{id}` - Verify configuration and provide customer details

**Business Value**: Enables rapid customer onboarding and revenue recognition.

### Workflow 2: Payment Issue Resolution
**Business Scenario**: Customer reports billing problem requiring investigation and resolution.

**Steps**:
1. `GET /api/admin/subscriptions?search=<EMAIL>` - Locate customer subscription
2. `GET /api/admin/subscriptions/{id}` - Review complete subscription context
3. `POST /api/admin/subscriptions/{id}/extend` - Provide grace period during resolution
4. `POST /api/admin/subscriptions/{id}/manual-payment` - Record resolved payment

**Business Value**: Maintains customer satisfaction and prevents churn due to billing issues.

### Workflow 3: Strategic Plan Management
**Business Scenario**: Product team launches new features requiring plan updates and new tier creation.

**Steps**:
1. `GET /api/admin/plans?includeInactive=true` - Review current plan portfolio
2. `POST /api/admin/plans` - Create new premium tier with advanced features
3. `PATCH /api/admin/plans/{id}/status` - Activate new plan for customer access

**Business Value**: Enables rapid market response and revenue expansion through new offerings.

### Workflow 4: Customer Success Upgrade
**Business Scenario**: Customer success team identifies expansion opportunity for high-usage customer.

**Steps**:
1. `GET /api/admin/subscriptions/{id}` - Review current subscription details
2. `GET /api/admin/subscriptions/{id}/usage` - Analyze usage patterns
3. `GET /api/admin/plans?userType=TransportCompany` - Identify upgrade options
4. `PUT /api/admin/subscriptions/{id}/upgrade` - Process upgrade with prorated billing

**Business Value**: Maximizes customer lifetime value and prevents churn from hitting limits.

### Workflow 5: Market Expansion Tax Setup
**Business Scenario**: Business expands to new geographic market requiring tax compliance setup.

**Steps**:
1. `GET /api/admin/tax/global-configurations` - Review existing tax configurations
2. `POST /api/admin/tax/global-configurations` - Add new market tax rules
3. `POST /api/admin/tax/exemptions` - Configure exemptions for qualified customers

**Business Value**: Enables compliant market expansion and competitive positioning.

## Error Handling with Business Context

### Common Error Responses

**400 Bad Request - Business Rule Violation**:
```json
{
  "type": "ValidationError",
  "title": "Validation failed",
  "status": 400,
  "errors": {
    "Price": ["Price must be greater than 0"],
    "Currency": ["Currency must be a valid 3-letter code"]
  }
}
```
**Business Impact**: Prevents invalid configurations that could cause billing or operational issues.

**404 Not Found - Resource Missing**:
```json
{
  "type": "NotFound",
  "title": "Resource not found",
  "status": 404,
  "detail": "Subscription with ID '550e8400-e29b-41d4-a716-446655440999' was not found"
}
```
**Business Impact**: Indicates data integrity issues or incorrect references requiring investigation.

**409 Conflict - Business Logic Violation**:
```json
{
  "type": "BusinessRuleViolation",
  "title": "Business rule violation",
  "status": 409,
  "detail": "Cannot upgrade to a plan of the same or lower tier"
}
```
**Business Impact**: Protects business logic and prevents operations that could cause revenue or customer experience issues.

## Security and Compliance

- **Admin Role Required**: Protects sensitive business operations from unauthorized access
- **Audit Logging**: Maintains compliance with financial regulations and internal policies
- **Input Validation**: Prevents data corruption and security vulnerabilities
- **Rate Limiting**: Protects system performance and prevents abuse

## Business Metrics and KPIs Enabled

This API enables tracking of key business metrics:

- **Monthly Recurring Revenue (MRR)**: Subscription revenue tracking
- **Customer Lifetime Value (CLV)**: Usage and upgrade pattern analysis
- **Churn Rate**: Cancellation and downgrade tracking
- **Plan Performance**: Adoption and revenue by plan tier
- **Payment Success Rate**: Billing and collection efficiency
- **Customer Support Efficiency**: Issue resolution tracking
- **Tax Compliance**: Accurate tax calculation and reporting
- **Usage Optimization**: Feature utilization and limit management

This comprehensive API framework supports the complete subscription business lifecycle while maintaining operational efficiency and customer satisfaction.