# Comprehensive Filtering Implementation for Subscription Management API

## Overview

This document describes the comprehensive filtering capabilities implemented for the `/api/Plans/admin/all` endpoint in the SubscriptionManagement service. The implementation provides advanced filtering, searching, and pagination functionality for plan management.

## Features Implemented

### 1. Filter Options

The API now supports the following filter parameters:

#### **User Type Filter** (`userType`)
- **Type**: `UserType` enum (optional)
- **Values**: `TransportCompany`, `Carrier`, `Broker`, `Driver`, `Shipper`, `ShipperCompany`, `ShipperIndividual`, `Transporter`, `Admin`, `SubAdmin`, `umsTransportCompany`, `umsCarrierCompany`, `umsShipperCompany`
- **Description**: Filters plans based on the target user type

#### **Plan Status Filter** (`isActive`)
- **Type**: `boolean` (optional)
- **Values**: `true` (active plans), `false` (inactive plans), `null` (all plans)
- **Description**: Filters plans based on their active/inactive status

#### **Search by Plan Name** (`searchTerm`)
- **Type**: `string` (optional)
- **Description**: Performs partial, case-insensitive search on plan names
- **Max Length**: 100 characters
- **Example**: Searching for "basic" will match "Basic Plan", "BASIC Transport", "Pro Basic", etc.

#### **Plan Type Filter** (`planType`)
- **Type**: `PlanType` enum (optional)
- **Values**: `Basic`, `Standard`, `Pro`, `Premium`, `Enterprise`
- **Description**: Filters plans based on their subscription type/tier



### 2. Pagination

- **Page**: 1-based page number (default: 1, min: 1)
- **Page Size**: Number of items per page (default: 10, min: 1, max: 100)

### 3. Response Structure

```json
{
  "plans": [
    {
      "id": "guid",
      "name": "string",
      "description": "string",
      "version": "string",
      "type": "PlanType",
      "userType": "UserType",
      "price": "decimal",
      "currency": "string",
      "billingCycle": "string",
      "isActive": "boolean",
      "isPublic": "boolean",
      "trialPeriodDays": "int?",
      "setupFee": "decimal?",
      "setupFeeCurrency": "string?",
      "createdAt": "datetime",
      "updatedAt": "datetime?",
      "createdBy": "string?",
      "updatedBy": "string?",
      "limits": { /* PlanLimitsDto */ },
      "features": [ /* PlanFeatureDto[] */ ]
    }
  ],
  "totalCount": "int",
  "page": "int",
  "pageSize": "int",
  "totalPages": "int",
  "hasNextPage": "boolean",
  "hasPreviousPage": "boolean",
  "filters": {
    "userType": "UserType?",
    "isActive": "boolean?",
    "searchTerm": "string?",
    "planType": "PlanType?",
    "activeFiltersCount": "int"
  }
}
```

## API Usage Examples

### Basic Usage
```http
GET /api/Plans/admin/all?page=1&pageSize=10
```

### Filter by User Type
```http
GET /api/Plans/admin/all?userType=TransportCompany
```

### Filter by Active Status
```http
GET /api/Plans/admin/all?isActive=true
```

### Search by Plan Name
```http
GET /api/Plans/admin/all?searchTerm=basic
```

### Filter by Plan Type
```http
GET /api/Plans/admin/all?planType=Pro
```



### Combined Filters
```http
GET /api/Plans/admin/all?userType=TransportCompany&isActive=true&searchTerm=basic&planType=Basic&page=1&pageSize=5
```

## Implementation Details

### Architecture

The implementation follows Clean Architecture principles with CQRS pattern:

1. **Controller Layer**: `PlansController.GetAllPlans()`
2. **Application Layer**: `GetAllPlansQuery` and `GetAllPlansQueryHandler`
3. **Domain Layer**: `Plan` entity with filtering logic
4. **Infrastructure Layer**: `PlanRepository.GetFilteredPlansAsync()`

### Key Components

#### 1. Query Object (`GetAllPlansQuery`)
- Contains all filter parameters
- Includes validation logic
- Supports constructor-based initialization

#### 2. Query Handler (`GetAllPlansQueryHandler`)
- Validates input parameters
- Sanitizes search terms
- Handles error scenarios
- Maps domain entities to DTOs

#### 3. Repository Method (`GetFilteredPlansAsync`)
- Implements efficient database queries
- Uses Entity Framework LINQ for filtering
- Supports pagination
- Returns both results and total count

#### 4. Response Object (`GetAllPlansResponse`)
- Comprehensive pagination information
- Filter summary with active filter count
- Navigation helpers (HasNextPage, HasPreviousPage)

### Performance Considerations

1. **Database Indexing**: Queries are optimized for common filter combinations
2. **Pagination**: Efficient SKIP/TAKE implementation
3. **Caching**: CachedPlanRepository bypasses cache for complex filtered queries
4. **Query Optimization**: Uses Entity Framework's optimized LINQ queries

### Error Handling

The implementation includes comprehensive error handling:

- **400 Bad Request**: Invalid pagination parameters or search term too long
- **401 Unauthorized**: Missing authentication
- **403 Forbidden**: Insufficient permissions (Admin role required)
- **500 Internal Server Error**: Unexpected server errors

### Validation Rules

1. **Page**: Must be ≥ 1
2. **PageSize**: Must be between 1 and 100
3. **SearchTerm**: Maximum 100 characters
4. **Enum Values**: Must be valid enum values for UserType and PlanType

## Testing

Comprehensive test suite includes:

1. **Unit Tests**: `GetAllPlansQueryHandlerTests`
   - Parameter validation
   - Filter logic verification
   - Error handling scenarios

2. **Integration Tests**: `PlanFilteringIntegrationTests`
   - Database query testing
   - Filter combination testing
   - Pagination verification

3. **API Tests**: `PlansControllerFilteringTests`
   - End-to-end API testing
   - HTTP status code verification
   - Response structure validation

## Security

- **Authorization**: Admin role required for accessing the endpoint
- **Input Sanitization**: Search terms are sanitized to prevent injection attacks
- **Parameter Validation**: All input parameters are validated before processing

## Future Enhancements

Potential improvements for future versions:

1. **Advanced Search**: Full-text search capabilities
2. **Sorting**: Multiple sorting options (name, price, created date)
3. **Date Filters**: Filter by creation date, modification date
4. **Bulk Operations**: Support for bulk status updates
5. **Export**: CSV/Excel export functionality
6. **Caching**: Smart caching for frequently used filter combinations

## Conclusion

This implementation provides a robust, scalable, and user-friendly filtering system for the SubscriptionManagement service. It follows best practices for API design, performance optimization, and maintainability while providing comprehensive functionality for plan management operations.
