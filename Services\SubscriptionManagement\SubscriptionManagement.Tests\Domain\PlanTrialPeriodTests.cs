using FluentAssertions;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Exceptions;
using SubscriptionManagement.Domain.ValueObjects;
using Xunit;

namespace SubscriptionManagement.Tests.Domain
{
    /// <summary>
    /// Tests for the TrialPeriodDays data type fix - ensuring it stores integer days instead of DateTime
    /// </summary>
    public class PlanTrialPeriodTests
    {
        [Fact]
        public void Plan_WithTrialPeriodDays_ShouldStoreAsInteger()
        {
            // Arrange
            var trialDays = 30;
            var price = Money.Create(99.99m, "INR");
            var billingCycle = BillingCycle.Monthly();
            var limits = PlanLimits.ForTransportCompany(10);

            // Act
            var plan = new Plan(
                "Test Plan",
                "Test Description",
                "1.0",
                PlanType.Basic,
                UserType.TransportCompany,
                price,
                billingCycle,
                limits,
                isPublic: true,
                trialPeriodDays: trialDays);

            // Assert
            plan.TrialPeriodDays.Should().Be(trialDays);
            plan.TrialPeriodDays.Should().BeOfType<int?>();
        }

        [Fact]
        public void Plan_WithNullTrialPeriodDays_ShouldStoreAsNull()
        {
            // Arrange
            var price = Money.Create(99.99m, "INR");
            var billingCycle = BillingCycle.Monthly();
            var limits = PlanLimits.ForTransportCompany(10);

            // Act
            var plan = new Plan(
                "Test Plan",
                "Test Description",
                "1.0",
                PlanType.Basic,
                UserType.TransportCompany,
                price,
                billingCycle,
                limits,
                isPublic: true,
                trialPeriodDays: null);

            // Assert
            plan.TrialPeriodDays.Should().BeNull();
        }

        [Fact]
        public void Plan_WithZeroTrialPeriodDays_ShouldThrowException()
        {
            // Arrange
            var price = Money.Create(99.99m, "INR");
            var billingCycle = BillingCycle.Monthly();
            var limits = PlanLimits.ForTransportCompany(10);

            // Act & Assert
            var exception = Assert.Throws<SubscriptionDomainException>(() => new Plan(
                "Test Plan",
                "Test Description",
                "1.0",
                PlanType.Basic,
                UserType.TransportCompany,
                price,
                billingCycle,
                limits,
                isPublic: true,
                trialPeriodDays: 0));

            exception.Message.Should().Be("Trial period days cannot be negative");
        }

        [Fact]
        public void Plan_WithNegativeTrialPeriodDays_ShouldThrowException()
        {
            // Arrange
            var price = Money.Create(99.99m, "INR");
            var billingCycle = BillingCycle.Monthly();
            var limits = PlanLimits.ForTransportCompany(10);

            // Act & Assert
            var exception = Assert.Throws<SubscriptionDomainException>(() => new Plan(
                "Test Plan",
                "Test Description",
                "1.0",
                PlanType.Basic,
                UserType.TransportCompany,
                price,
                billingCycle,
                limits,
                isPublic: true,
                trialPeriodDays: -5));

            exception.Message.Should().Be("Trial period days cannot be negative");
        }

        [Theory]
        [InlineData(1)]
        [InlineData(7)]
        [InlineData(14)]
        [InlineData(30)]
        [InlineData(90)]
        [InlineData(365)]
        public void Plan_WithValidTrialPeriodDays_ShouldStoreCorrectValue(int trialDays)
        {
            // Arrange
            var price = Money.Create(99.99m, "INR");
            var billingCycle = BillingCycle.Monthly();
            var limits = PlanLimits.ForTransportCompany(10);

            // Act
            var plan = new Plan(
                "Test Plan",
                "Test Description",
                "1.0",
                PlanType.Basic,
                UserType.TransportCompany,
                price,
                billingCycle,
                limits,
                isPublic: true,
                trialPeriodDays: trialDays);

            // Assert
            plan.TrialPeriodDays.Should().Be(trialDays);
            plan.TrialPeriodDays.Should().BeOfType<int?>();
            
            // Verify it's not storing a DateTime
            plan.TrialPeriodDays.Should().NotBeOfType<DateTime?>();
        }

        [Fact]
        public void Plan_TrialPeriodDays_ShouldNotBeCalculatedFromCurrentTime()
        {
            // Arrange
            var trialDays = 30;
            var price = Money.Create(99.99m, "INR");
            var billingCycle = BillingCycle.Monthly();
            var limits = PlanLimits.ForTransportCompany(10);
            var beforeCreation = DateTime.UtcNow;

            // Act
            var plan = new Plan(
                "Test Plan",
                "Test Description",
                "1.0",
                PlanType.Basic,
                UserType.TransportCompany,
                price,
                billingCycle,
                limits,
                isPublic: true,
                trialPeriodDays: trialDays);

            var afterCreation = DateTime.UtcNow;

            // Assert
            // The TrialPeriodDays should be exactly the input value, not a calculated DateTime
            plan.TrialPeriodDays.Should().Be(trialDays);
            
            // Verify it's not storing a future date based on creation time
            // (This was the bug - it was storing DateTime.UtcNow.AddDays(trialDays))
            plan.TrialPeriodDays.Should().NotBe((int)(beforeCreation.AddDays(trialDays) - DateTime.UtcNow).TotalDays);
            plan.TrialPeriodDays.Should().NotBe((int)(afterCreation.AddDays(trialDays) - DateTime.UtcNow).TotalDays);
        }

        [Fact]
        public void Plan_TrialPeriodDays_ShouldBeConsistentAcrossMultipleCreations()
        {
            // Arrange
            var trialDays = 15;
            var price = Money.Create(99.99m, "INR");
            var billingCycle = BillingCycle.Monthly();
            var limits = PlanLimits.ForTransportCompany(10);

            // Act - Create multiple plans with same trial period
            var plan1 = new Plan("Plan 1", "Description 1", "1.0", PlanType.Basic, UserType.TransportCompany, 
                price, billingCycle, limits, isPublic: true, trialPeriodDays: trialDays);
            
            // Wait a bit to ensure different creation times
            Thread.Sleep(10);
            
            var plan2 = new Plan("Plan 2", "Description 2", "1.0", PlanType.Basic, UserType.TransportCompany, 
                price, billingCycle, limits, isPublic: true, trialPeriodDays: trialDays);

            // Assert
            // Both plans should have the same TrialPeriodDays value regardless of creation time
            plan1.TrialPeriodDays.Should().Be(trialDays);
            plan2.TrialPeriodDays.Should().Be(trialDays);
            plan1.TrialPeriodDays.Should().Be(plan2.TrialPeriodDays);
        }
    }
}
