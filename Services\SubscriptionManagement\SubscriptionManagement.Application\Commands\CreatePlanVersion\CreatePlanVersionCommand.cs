using MediatR;
using SubscriptionManagement.Application.DTOs;

namespace SubscriptionManagement.Application.Commands.CreatePlanVersion
{
    /// <summary>
    /// Command to create a new version of a plan or get the current version for modification
    /// </summary>
    public class CreatePlanVersionCommand : IRequest<PlanVersionResponseDto>
    {
        /// <summary>
        /// The ID of the plan to create a new version for
        /// </summary>
        public Guid PlanId { get; set; }

        /// <summary>
        /// The current version number of the plan
        /// </summary>
        public int CurrentVersion { get; set; }

        public CreatePlanVersionCommand(Guid planId, int currentVersion)
        {
            PlanId = planId;
            CurrentVersion = currentVersion;
        }

        public CreatePlanVersionCommand() { }
    }
}
