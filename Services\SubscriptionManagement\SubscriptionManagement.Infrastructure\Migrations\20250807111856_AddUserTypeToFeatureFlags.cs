﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SubscriptionManagement.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddUserTypeToFeatureFlags : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Conditionally drop indexes only if they exist
            migrationBuilder.Sql(@"
                DO $$
                BEGIN
                    IF EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IX_UsageRecords_FeatureType') THEN
                        DROP INDEX ""IX_UsageRecords_FeatureType"";
                    END IF;

                    IF EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IX_UsageRecords_SubscriptionId_FeatureType_PeriodStart_PeriodE~') THEN
                        DROP INDEX ""IX_UsageRecords_SubscriptionId_FeatureType_PeriodStart_PeriodE~"";
                    END IF;

                    IF EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IX_UsageRecords_UserId_FeatureType_PeriodStart_PeriodEnd') THEN
                        DROP INDEX ""IX_UsageRecords_UserId_FeatureType_PeriodStart_PeriodEnd"";
                    END IF;

                    IF EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IX_PlanFeatures_PlanId_FeatureType') THEN
                        DROP INDEX ""IX_PlanFeatures_PlanId_FeatureType"";
                    END IF;
                END $$;
            ");

            // Conditionally drop columns only if they exist
            migrationBuilder.Sql(@"
                DO $$
                BEGIN
                    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'UsageRecords' AND column_name = 'FeatureType') THEN
                        ALTER TABLE ""UsageRecords"" DROP COLUMN ""FeatureType"";
                    END IF;

                    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'PlanFeatures' AND column_name = 'AccessType') THEN
                        ALTER TABLE ""PlanFeatures"" DROP COLUMN ""AccessType"";
                    END IF;

                    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'PlanFeatures' AND column_name = 'FeatureType') THEN
                        ALTER TABLE ""PlanFeatures"" DROP COLUMN ""FeatureType"";
                    END IF;

                    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'PlanFeatures' AND column_name = 'LimitValue') THEN
                        ALTER TABLE ""PlanFeatures"" DROP COLUMN ""LimitValue"";
                    END IF;
                END $$;
            ");

            // Conditionally add FeatureKey column only if it doesn't exist
            migrationBuilder.Sql(@"
                DO $$
                BEGIN
                    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'UsageRecords' AND column_name = 'FeatureKey') THEN
                        ALTER TABLE ""UsageRecords"" ADD ""FeatureKey"" character varying(100) NOT NULL DEFAULT '';
                    END IF;
                END $$;
            ");

            // Conditionally add Version column only if it doesn't exist
            migrationBuilder.Sql(@"
                DO $$
                BEGIN
                    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'Plans' AND column_name = 'Version') THEN
                        ALTER TABLE ""Plans"" ADD ""Version"" character varying(50) NOT NULL DEFAULT '';
                    END IF;
                END $$;
            ");

            // Conditionally add PlanFeatures columns only if they don't exist
            migrationBuilder.Sql(@"
                DO $$
                BEGIN
                    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'PlanFeatures' AND column_name = 'FeatureId') THEN
                        ALTER TABLE ""PlanFeatures"" ADD ""FeatureId"" uuid NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000';
                    END IF;

                    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'PlanFeatures' AND column_name = 'Key') THEN
                        ALTER TABLE ""PlanFeatures"" ADD ""Key"" character varying(100) NOT NULL DEFAULT '';
                    END IF;

                    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'PlanFeatures' AND column_name = 'Name') THEN
                        ALTER TABLE ""PlanFeatures"" ADD ""Name"" character varying(200) NOT NULL DEFAULT '';
                    END IF;
                END $$;
            ");

            migrationBuilder.AlterColumn<string>(
                name: "Metadata",
                table: "FeatureFlags",
                type: "jsonb",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "jsonb");

            // Conditionally add UserType column only if it doesn't exist
            migrationBuilder.Sql(@"
                DO $$
                BEGIN
                    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'FeatureFlags' AND column_name = 'UserType') THEN
                        ALTER TABLE ""FeatureFlags"" ADD ""UserType"" integer NULL;
                    END IF;
                END $$;
            ");

            // Conditionally create indexes only if they don't exist
            migrationBuilder.Sql(@"
                DO $$
                BEGIN
                    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IX_UsageRecords_FeatureKey') THEN
                        CREATE INDEX ""IX_UsageRecords_FeatureKey"" ON ""UsageRecords"" (""FeatureKey"");
                    END IF;

                    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IX_UsageRecords_SubscriptionId_FeatureKey_PeriodStart_PeriodEnd') THEN
                        CREATE INDEX ""IX_UsageRecords_SubscriptionId_FeatureKey_PeriodStart_PeriodEnd"" ON ""UsageRecords"" (""SubscriptionId"", ""FeatureKey"", ""PeriodStart"", ""PeriodEnd"");
                    END IF;

                    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IX_UsageRecords_UserId_FeatureKey_PeriodStart_PeriodEnd') THEN
                        CREATE INDEX ""IX_UsageRecords_UserId_FeatureKey_PeriodStart_PeriodEnd"" ON ""UsageRecords"" (""UserId"", ""FeatureKey"", ""PeriodStart"", ""PeriodEnd"");
                    END IF;

                    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IX_PlanFeatures_Key') THEN
                        CREATE INDEX ""IX_PlanFeatures_Key"" ON ""PlanFeatures"" (""Key"");
                    END IF;

                    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IX_PlanFeatures_PlanId_FeatureId') THEN
                        CREATE UNIQUE INDEX ""IX_PlanFeatures_PlanId_FeatureId"" ON ""PlanFeatures"" (""PlanId"", ""FeatureId"");
                    END IF;

                    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'IX_FeatureFlags_UserType') THEN
                        CREATE INDEX ""IX_FeatureFlags_UserType"" ON ""FeatureFlags"" (""UserType"");
                    END IF;
                END $$;
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_UsageRecords_FeatureKey",
                table: "UsageRecords");

            migrationBuilder.DropIndex(
                name: "IX_UsageRecords_SubscriptionId_FeatureKey_PeriodStart_PeriodEnd",
                table: "UsageRecords");

            migrationBuilder.DropIndex(
                name: "IX_UsageRecords_UserId_FeatureKey_PeriodStart_PeriodEnd",
                table: "UsageRecords");

            migrationBuilder.DropIndex(
                name: "IX_PlanFeatures_Key",
                table: "PlanFeatures");

            migrationBuilder.DropIndex(
                name: "IX_PlanFeatures_PlanId_FeatureId",
                table: "PlanFeatures");

            migrationBuilder.DropIndex(
                name: "IX_FeatureFlags_UserType",
                table: "FeatureFlags");

            migrationBuilder.DropColumn(
                name: "FeatureKey",
                table: "UsageRecords");

            migrationBuilder.DropColumn(
                name: "Version",
                table: "Plans");

            migrationBuilder.DropColumn(
                name: "FeatureId",
                table: "PlanFeatures");

            migrationBuilder.DropColumn(
                name: "Key",
                table: "PlanFeatures");

            migrationBuilder.DropColumn(
                name: "Name",
                table: "PlanFeatures");

            migrationBuilder.DropColumn(
                name: "UserType",
                table: "FeatureFlags");

            migrationBuilder.AddColumn<string>(
                name: "FeatureType",
                table: "UsageRecords",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "AccessType",
                table: "PlanFeatures",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "FeatureType",
                table: "PlanFeatures",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "LimitValue",
                table: "PlanFeatures",
                type: "integer",
                nullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Metadata",
                table: "FeatureFlags",
                type: "jsonb",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "jsonb",
                oldNullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_UsageRecords_FeatureType",
                table: "UsageRecords",
                column: "FeatureType");

            migrationBuilder.CreateIndex(
                name: "IX_UsageRecords_SubscriptionId_FeatureType_PeriodStart_PeriodE~",
                table: "UsageRecords",
                columns: new[] { "SubscriptionId", "FeatureType", "PeriodStart", "PeriodEnd" });

            migrationBuilder.CreateIndex(
                name: "IX_UsageRecords_UserId_FeatureType_PeriodStart_PeriodEnd",
                table: "UsageRecords",
                columns: new[] { "UserId", "FeatureType", "PeriodStart", "PeriodEnd" });

            migrationBuilder.CreateIndex(
                name: "IX_PlanFeatures_PlanId_FeatureType",
                table: "PlanFeatures",
                columns: new[] { "PlanId", "FeatureType" },
                unique: true);
        }
    }
}
