using Microsoft.EntityFrameworkCore.Migrations;
using System;

#nullable disable

namespace SubscriptionManagement.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class CreateTierConfigurationSetupTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Create TierConfigurationSetup table only if it doesn't exist
            migrationBuilder.Sql(@"
                DO $$
                BEGIN
                    IF NOT EXISTS (
                        SELECT 1 FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = 'TierConfigurationSetup'
                    ) THEN
                        CREATE TABLE ""TierConfigurationSetup"" (
                            ""Id"" uuid NOT NULL,
                            ""Type"" character varying(255) NOT NULL,
                            ""CreatedOn"" timestamp with time zone NOT NULL,
                            ""UpdatedOn"" timestamp with time zone,
                            ""UpdatedBy"" character varying(255) NOT NULL,
                            ""CreatedBy"" character varying(255) NOT NULL,
                            ""IsActive"" boolean NOT NULL DEFAULT true,
                            ""IsCustom"" boolean,
                            ""CreatedAt"" timestamp with time zone NOT NULL,
                            ""UpdatedAt"" timestamp with time zone,
                            CONSTRAINT ""PK_TierConfigurationSetup"" PRIMARY KEY (""Id"")
                        );

                        -- Create indexes
                        CREATE INDEX ""IX_TierConfigurationSetup_Type"" ON ""TierConfigurationSetup"" (""Type"");
                        CREATE INDEX ""IX_TierConfigurationSetup_IsActive"" ON ""TierConfigurationSetup"" (""IsActive"");
                        CREATE INDEX ""IX_TierConfigurationSetup_CreatedBy"" ON ""TierConfigurationSetup"" (""CreatedBy"");
                        CREATE INDEX ""IX_TierConfigurationSetup_CreatedOn"" ON ""TierConfigurationSetup"" (""CreatedOn"");
                    END IF;
                END $$;
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "TierConfigurationSetup");
        }
    }
}
